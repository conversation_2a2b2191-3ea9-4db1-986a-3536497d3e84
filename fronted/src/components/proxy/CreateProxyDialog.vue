<template>
  <a-modal
    v-model:visible="modalVisible"
    title="创建代理实例"
    width="800px"
    :confirm-loading="submitting"
    @ok="handleSubmit"
    @cancel="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="代理名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入代理名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="代理类型" prop="type">
            <el-select v-model="form.type" placeholder="选择代理类型" style="width: 100%">
              <el-option label="正向代理" value="forward" />
              <el-option label="反向代理" value="reverse" />
              <el-option label="代理链" value="chain" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="2"
          placeholder="请输入代理描述（可选）"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="监听端口" prop="port">
            <el-input-number
              v-model="form.port"
              :min="28000"
              :max="48000"
              placeholder="请输入端口号 (28000-48000)"
              style="width: 100%"
            />
            <div style="font-size: 12px; color: #999; margin-top: 4px;">
              系统将在 28000-48000 范围内自动分配可用端口
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联客户端" prop="client_id">
            <el-select v-model="form.client_id" placeholder="选择客户端" style="width: 100%">
              <el-option
                v-for="client in clientList"
                :key="client.id"
                :label="`${client.hostname} (${client.client_id})`"
                :value="client.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 认证设置 -->
      <el-divider content-position="left">认证设置</el-divider>
      <el-form-item>
        <el-checkbox v-model="form.auth_required">启用认证</el-checkbox>
      </el-form-item>

      <el-row :gutter="20" v-if="form.auth_required">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="form.username" placeholder="请输入用户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 高级设置 -->
      <el-divider content-position="left">高级设置</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最大连接数">
            <el-input-number
              v-model="form.max_connections"
              :min="1"
              :max="10000"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="超时时间(秒)">
            <el-input-number
              v-model="form.timeout"
              :min="1"
              :max="3600"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="缓冲区大小">
            <el-input-number
              v-model="form.buffer_size"
              :min="1024"
              :max="65536"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="日志级别">
            <el-select v-model="form.log_level" style="width: 100%">
              <el-option label="Debug" value="debug" />
              <el-option label="Info" value="info" />
              <el-option label="Warn" value="warn" />
              <el-option label="Error" value="error" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 访问控制 -->
      <el-divider content-position="left">访问控制</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="允许的IP">
            <el-input
              v-model="form.allowed_ips"
              placeholder="多个IP用逗号分隔，留空表示允许所有"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="阻止的IP">
            <el-input
              v-model="form.blocked_ips"
              placeholder="多个IP用逗号分隔"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 速率限制 -->
      <el-divider content-position="left">速率限制</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="请求限制(RPS)">
            <el-input-number
              v-model="form.rate_limit_rps"
              :min="0"
              placeholder="0表示不限制"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="带宽限制(B/s)">
            <el-input-number
              v-model="form.rate_limit_bytes"
              :min="0"
              placeholder="0表示不限制"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 其他选项 -->
      <el-divider content-position="left">其他选项</el-divider>
      <el-form-item>
        <el-checkbox v-model="form.enable_metrics">启用指标收集</el-checkbox>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createProxy } from '@/api/proxy'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  clientList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const dialogVisible = ref(false)

// 表单数据
const form = reactive({
  name: '',
  description: '',
  type: 'forward',
  port: 28000,
  client_id: null,
  auth_required: false,
  username: '',
  password: '',
  max_connections: 100,
  timeout: 30,
  buffer_size: 4096,
  log_level: 'info',
  allowed_ips: '',
  blocked_ips: '',
  rate_limit_rps: 0,
  rate_limit_bytes: 0,
  enable_metrics: true
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入代理名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择代理类型', trigger: 'change' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在 1-65535 之间', trigger: 'blur' }
  ],
  client_id: [
    { required: true, message: '请选择关联客户端', trigger: 'change' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur', validator: validateUsername }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur', validator: validatePassword }
  ]
}

// 自定义验证器
function validateUsername(rule, value, callback) {
  if (form.auth_required && !value) {
    callback(new Error('启用认证时用户名不能为空'))
  } else {
    callback()
  }
}

function validatePassword(rule, value, callback) {
  if (form.auth_required && !value) {
    callback(new Error('启用认证时密码不能为空'))
  } else {
    callback()
  }
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    name: '',
    description: '',
    type: 'forward',
    port: 8080,
    client_id: null,
    auth_required: false,
    username: '',
    password: '',
    max_connections: 100,
    timeout: 30,
    buffer_size: 4096,
    log_level: 'info',
    allowed_ips: '',
    blocked_ips: '',
    rate_limit_rps: 0,
    rate_limit_bytes: 0,
    enable_metrics: true
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData = { ...form }
    
    // 如果没有启用认证，清空用户名和密码
    if (!submitData.auth_required) {
      submitData.username = ''
      submitData.password = ''
    }
    
    const response = await createProxy(submitData)
    
    if (response.code === 200) {
      ElMessage.success('代理创建成功')
      emit('success')
    } else {
      ElMessage.error(response.msg || '代理创建失败')
    }
  } catch (error) {
    if (error !== false) { // 表单验证失败时会返回false
      console.error('创建代理失败:', error)
      ElMessage.error('创建代理失败')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #409eff;
}
</style>
