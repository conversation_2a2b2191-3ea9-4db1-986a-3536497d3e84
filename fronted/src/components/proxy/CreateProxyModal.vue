<template>
  <a-modal
    v-model:visible="modalVisible"
    title="创建代理实例"
    :width="modalWidth"
    :confirm-loading="submitting"
    @ok="handleSubmit"
    @cancel="handleClose"
    :body-style="{ maxHeight: '70vh', overflowY: 'auto' }"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ xs: { span: 24 }, sm: { span: 8 }, md: { span: 6 }, lg: { span: 6 } }"
      :wrapper-col="{ xs: { span: 24 }, sm: { span: 16 }, md: { span: 18 }, lg: { span: 18 } }"
    >
      <!-- 基本信息 -->
      <a-divider orientation="left">基本信息</a-divider>
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="代理名称" name="name">
            <a-input v-model:value="form.name" placeholder="请输入代理名称" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="代理类型" name="type">
            <a-select v-model:value="form.type" placeholder="选择代理类型">
              <a-select-option value="forward">正向代理</a-select-option>
              <a-select-option value="reverse">反向代理</a-select-option>
              <a-select-option value="chain">代理链</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="描述">
        <a-textarea
          v-model:value="form.description"
          :rows="2"
          placeholder="请输入代理描述（可选）"
        />
      </a-form-item>

      <a-row :gutter="[16, 16]">
        <a-col :span="24">
          <!-- 使用新的端口管理组件 -->
          <PortManager
            :proxy-type="form.type"
            label="端口配置"
            v-model="form.portConfig"
          />
        </a-col>
      </a-row>

      <!-- iox代理类型特殊说明 -->
      <template v-if="form.type === 'forward'">
        <a-alert
          message="iox正向代理模式"
          description="Client启动SOCKS5服务器，Server连接Client，用户通过Server端口访问Client所在内网的资源。适用于横向移动、内网探测、权限提升等场景。"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>

      <template v-if="form.type === 'reverse'">
        <a-alert
          message="iox反向代理模式"
          description="Server开启双端口服务，Client主动连接，外部用户可以通过Server访问Client内网的特定服务。适用于服务暴露、数据提取、持久化访问等场景。"
          type="warning"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>

      <template v-if="form.type === 'chain'">
        <a-alert
          message="iox代理链模式"
          description="多个Client协同工作自动搭建代理链，穿越多层内网环境，用户连接最终SOCKS5端口即可访问深层内网资源。适用于深层渗透、复杂拓扑访问等场景。"
          type="success"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>

      <!-- 代理链模式：节点管理 -->
      <template v-if="form.type === 'chain'">
        <ProxyChainNodeManager
          v-model="form.chain_nodes"
          :available-proxies="availableProxies"
          @refresh="loadAvailableProxies"
        />
      </template>

      <!-- 非代理链模式：关联客户端 -->
      <template v-else>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="关联客户端" name="client_id">
              <a-select v-model:value="form.client_id" placeholder="选择客户端">
                <a-select-option
                  v-for="client in clientList"
                  :key="client.id"
                  :value="client.id"
                >
                  {{ client.hostname }} ({{ client.client_id }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <a-form-item label="优先级" name="priority">
              <a-input-number
                v-model:value="form.priority"
                :min="1"
                :max="999"
                placeholder="请输入优先级 (1-999)"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999; margin-top: 4px;">
                数值越小优先级越高
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <!-- 认证设置 -->
      <a-divider orientation="left">认证设置</a-divider>
      <a-form-item>
        <a-checkbox v-model:checked="form.auth_required">启用认证</a-checkbox>
        <div style="margin-top: 4px; color: #666; font-size: 12px;">
          <template v-if="form.type === 'forward'">
            🔒 正向代理建议启用认证，防止内网访问权限被滥用
          </template>
          <template v-else-if="form.type === 'reverse'">
            🔒 反向代理建议启用认证，保护暴露的内网服务安全
          </template>
          <template v-else>
            🔒 代理链强烈建议启用认证，保护整个深层访问链路
          </template>
        </div>
      </a-form-item>

      <a-row :gutter="[16, 16]" v-if="form.auth_required">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="用户名" name="username">
            <a-input v-model:value="form.username" placeholder="请输入用户名" />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="密码" name="password">
            <a-input-password
              v-model:value="form.password"
              placeholder="请输入密码"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 高级设置 -->
      <a-divider orientation="left">高级设置</a-divider>
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="最大连接数">
            <a-input-number
              v-model:value="form.max_connections"
              :min="1"
              :max="10000"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="超时时间(秒)">
            <a-input-number
              v-model:value="form.timeout"
              :min="1"
              :max="3600"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="缓冲区大小">
            <a-input-number
              v-model:value="form.buffer_size"
              :min="1024"
              :max="65536"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="日志级别">
            <a-select v-model:value="form.log_level">
              <a-select-option value="debug">Debug</a-select-option>
              <a-select-option value="info">Info</a-select-option>
              <a-select-option value="warn">Warn</a-select-option>
              <a-select-option value="error">Error</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 反向代理特有配置 -->
      <template v-if="form.type === 'reverse'">
        <a-divider orientation="left">反向代理特有配置</a-divider>
        <a-alert
          message="反向代理连接管理"
          description="由于Client主动连接Server，需要特别配置连接保活、心跳检测等参数确保连接稳定性。"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <a-form-item label="连接保活(秒)">
              <a-input-number
                v-model:value="form.keep_alive_interval"
                :min="10"
                :max="300"
                placeholder="60"
                style="width: 100%"
              />
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                💡 Client向Server发送保活包的间隔时间
              </div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <a-form-item label="心跳检测(秒)">
              <a-input-number
                v-model:value="form.heartbeat_interval"
                :min="5"
                :max="120"
                placeholder="30"
                style="width: 100%"
              />
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                💡 Server检测Client连接状态的间隔时间
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <a-form-item label="Client连接超时(秒)">
              <a-input-number
                v-model:value="form.client_connect_timeout"
                :min="5"
                :max="300"
                placeholder="30"
                style="width: 100%"
              />
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                💡 等待Client连接的最大超时时间
              </div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <a-form-item label="重连间隔(秒)">
              <a-input-number
                v-model:value="form.reconnect_interval"
                :min="1"
                :max="60"
                placeholder="5"
                style="width: 100%"
              />
              <div style="margin-top: 4px; color: #666; font-size: 12px;">
                💡 Client断线后自动重连的间隔时间
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <!-- 访问控制 -->
      <a-divider orientation="left">访问控制</a-divider>
      <template v-if="form.type === 'forward'">
        <a-alert
          message="正向代理访问控制"
          description="建议限制允许连接的IP范围，防止内网访问权限被滥用。可以设置特定的管理员IP或内网IP段。"
          type="warning"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="允许的IP" name="allowed_ips">
            <a-input
              v-model:value="form.allowed_ips"
              placeholder="多个IP用逗号分隔，留空表示允许所有"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              <template v-if="form.type === 'forward'">
                💡 建议设置：管理员IP、内网IP段 (如: ***********/24)
              </template>
              <template v-else>
                💡 建议设置：可信任的外部IP地址
              </template>
            </div>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-form-item label="阻止的IP" name="blocked_ips">
            <a-input
              v-model:value="form.blocked_ips"
              placeholder="多个IP用逗号分隔"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              💡 可设置已知的恶意IP或不信任的IP段
            </div>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 速率限制 -->
      <a-divider orientation="left">速率限制</a-divider>
      <template v-if="form.type === 'forward'">
        <a-alert
          message="正向代理速率控制"
          description="合理设置速率限制可以防止内网访问被滥用，保护Client和Server的资源。建议根据实际需求设置适当的限制。"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
      </template>
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="请求限制(RPS)">
            <a-input-number
              v-model:value="form.rate_limit_rps"
              :min="0"
              placeholder="0表示不限制"
              style="width: 100%"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              <template v-if="form.type === 'forward'">
                💡 建议设置：100-1000 RPS (根据内网访问需求)
              </template>
              <template v-else>
                💡 建议设置：根据服务类型调整限制
              </template>
            </div>
          </a-form-item>
        </a-col>
        <a-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <a-form-item label="带宽限制(B/s)">
            <a-input-number
              v-model:value="form.rate_limit_bytes"
              :min="0"
              placeholder="0表示不限制"
              style="width: 100%"
            />
            <div style="margin-top: 4px; color: #666; font-size: 12px;">
              💡 建议设置：1MB/s - 10MB/s (根据网络环境)
            </div>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 其他选项 -->
      <a-divider orientation="left">其他选项</a-divider>
      <a-form-item>
        <a-checkbox v-model:checked="form.enable_metrics">启用指标收集</a-checkbox>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import { createProxy } from '@/api/proxy'
import PortManager from './PortManager.vue'
import ProxyChainNodeManager from './ProxyChainNodeManager.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  clientList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const modalVisible = ref(false)
const availableProxies = ref([])  // 可用代理实例列表

// 响应式模态框宽度
const modalWidth = computed(() => {
  if (typeof window !== 'undefined') {
    const width = window.innerWidth
    if (width < 576) return '95%'      // 超小屏幕
    if (width < 768) return '90%'      // 小屏幕
    if (width < 992) return '85%'      // 中等屏幕
    if (width < 1200) return '800px'   // 大屏幕
    return '900px'                     // 超大屏幕
  }
  return '800px'
})

// 表单数据
const form = reactive({
  name: '',
  description: '',
  type: 'forward',
  portConfig: { mode: 'auto', port: null }, // 端口配置
  client_id: undefined,
  priority: 1, // 优先级
  auth_required: false,
  username: '',
  password: '',
  max_connections: 100,
  timeout: 30,
  buffer_size: 4096,
  log_level: 'info',
  allowed_ips: '',
  blocked_ips: '',
  rate_limit_rps: 0,
  rate_limit_bytes: 0,
  enable_metrics: true,
  // 反向代理特有配置
  keep_alive_interval: 60,
  heartbeat_interval: 30,
  client_connect_timeout: 30,
  reconnect_interval: 5,
  // 代理链特有配置
  chain_nodes: []
})

// 表单验证规则 - 适配iox代理系统标准
const rules = {
  // 代理名称验证 - 支持iox命名规范
  name: [
    { required: true, message: '请输入代理名称', trigger: 'blur' },
    { min: 2, max: 64, message: '长度在 2 到 64 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,
      message: '只能包含字母、数字、中文、下划线和连字符',
      trigger: 'blur'
    }
  ],

  // 代理类型验证
  type: [
    { required: true, message: '请选择代理类型', trigger: 'change' }
  ],

  // 客户端验证 - 适配iox客户端管理
  client_id: [
    {
      validator: (rule, value) => {
        // 代理链模式不需要验证client_id
        if (form.type === 'chain') {
          return Promise.resolve()
        }
        if (!value) {
          return Promise.reject('请选择关联客户端')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ],

  // 代理链节点验证 - iox代理链标准
  chain_nodes: [
    {
      validator: (rule, value) => {
        if (form.type === 'chain') {
          if (!value || value.length < 2) {
            return Promise.reject('代理链至少需要2个节点')
          }
          if (value.length > 10) {
            return Promise.reject('代理链节点数量不能超过10个')
          }
          // 验证节点类型组合的合理性
          const forwardCount = value.filter(node => node.type === 'forward').length
          const reverseCount = value.filter(node => node.type === 'reverse').length
          if (forwardCount === 0) {
            return Promise.reject('代理链至少需要一个正向代理节点')
          }
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ],

  // 优先级验证 - iox优先级标准
  priority: [
    { required: true, message: '请输入优先级', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '优先级必须在 1-999 之间', trigger: 'blur' }
  ],

  // 认证用户名验证 - iox认证标准
  username: [
    {
      validator: (rule, value) => {
        if (form.auth_required) {
          if (!value) {
            return Promise.reject('启用认证时用户名不能为空')
          }
          if (value.length < 3 || value.length > 32) {
            return Promise.reject('用户名长度必须在 3-32 个字符之间')
          }
          if (!/^[a-zA-Z0-9_]+$/.test(value)) {
            return Promise.reject('用户名只能包含字母、数字和下划线')
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],

  // 认证密码验证 - iox安全标准
  password: [
    {
      validator: (rule, value) => {
        if (form.auth_required) {
          if (!value) {
            return Promise.reject('启用认证时密码不能为空')
          }
          if (value.length < 8) {
            return Promise.reject('密码长度至少8个字符')
          }
          if (value.length > 128) {
            return Promise.reject('密码长度不能超过128个字符')
          }
          // 强密码验证（至少包含字母和数字）
          if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(value)) {
            return Promise.reject('密码必须包含至少一个字母和一个数字')
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],

  // 端口配置验证 - iox端口范围标准
  'portConfig.port': [
    {
      validator: (rule, value) => {
        if (form.portConfig.mode === 'manual' && value) {
          const port = parseInt(value)
          if (isNaN(port) || port < 1024 || port > 65535) {
            return Promise.reject('端口必须在 1024-65535 之间')
          }
          // iox保留端口检查
          const reservedPorts = [8888, 8080, 3389, 22, 80, 443]
          if (reservedPorts.includes(port)) {
            return Promise.reject('该端口为系统保留端口，请选择其他端口')
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],

  // IP地址验证 - 访问控制
  allowed_ips: [
    {
      validator: (rule, value) => {
        if (value && value.trim()) {
          const ips = value.split(',').map(ip => ip.trim())
          for (const ip of ips) {
            // 支持IP地址和CIDR格式
            if (!/^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/.test(ip)) {
              return Promise.reject('IP地址格式不正确，支持格式：*********** 或 ***********/24')
            }
            // 验证IP地址范围
            const ipParts = ip.split('/')[0].split('.')
            for (const part of ipParts) {
              const num = parseInt(part)
              if (num < 0 || num > 255) {
                return Promise.reject('IP地址范围不正确，每段必须在0-255之间')
              }
            }
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],

  // 阻止IP验证 - 访问控制
  blocked_ips: [
    {
      validator: (rule, value) => {
        if (value && value.trim()) {
          const ips = value.split(',').map(ip => ip.trim())
          for (const ip of ips) {
            // 支持IP地址和CIDR格式
            if (!/^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/.test(ip)) {
              return Promise.reject('IP地址格式不正确，支持格式：*********** 或 ***********/24')
            }
            // 验证IP地址范围
            const ipParts = ip.split('/')[0].split('.')
            for (const part of ipParts) {
              const num = parseInt(part)
              if (num < 0 || num > 255) {
                return Promise.reject('IP地址范围不正确，每段必须在0-255之间')
              }
            }
          }
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

// 监听对话框显示状态
watch(() => props.visible, (val) => {
  modalVisible.value = val
  if (val) {
    // 弹窗打开时加载可用代理列表
    loadAvailableProxies()
  }
})

watch(modalVisible, (val) => {
  emit('update:visible', val)
})

// 方法
const handleClose = () => {
  modalVisible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    name: '',
    description: '',
    type: 'forward',
    portConfig: { mode: 'auto', port: null }, // 端口配置
    client_id: undefined,
    priority: 1, // 优先级
    auth_required: false,
    username: '',
    password: '',
    max_connections: 100,
    timeout: 30,
    buffer_size: 4096,
    log_level: 'info',
    allowed_ips: '',
    blocked_ips: '',
    rate_limit_rps: 0,
    rate_limit_bytes: 0,
    enable_metrics: true
  })
}

// 加载可用代理列表
const loadAvailableProxies = async () => {
  try {
    // TODO: 实际应该从API获取真实代理数据
    // 以下是测试示例数据，用于演示代理链功能
    availableProxies.value = [
      {
        id: 'test-proxy-1',
        name: '[测试] 外网代理-1',
        type: 'forward',
        client_name: 'TestClient-*************',
        status: 'running',
        port: 28001,
        description: '测试示例：目标外网系统，最接近Server',
        isTestData: true
      },
      {
        id: 'test-proxy-2',
        name: '[测试] 第一层内网代理-1',
        type: 'reverse',
        client_name: 'TestClient-*************',
        status: 'running',
        port: 28002,
        description: '测试示例：通过外网代理访问到的第一层内网',
        isTestData: true
      },
      {
        id: 'test-proxy-3',
        name: '[测试] 第二层内网代理-1',
        type: 'forward',
        client_name: 'TestClient-**********',
        status: 'running',
        port: 28003,
        description: '测试示例：通过第一层内网访问到的深层内网',
        isTestData: true
      }
    ]
  } catch (error) {
    console.error('加载可用代理列表失败:', error)
    message.error('加载可用代理列表失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const submitData = { ...form }

    // 处理端口配置
    if (form.portConfig.mode === 'manual' && form.portConfig.port) {
      submitData.port = form.portConfig.port
    } else {
      // 自动分配模式，传递0表示由后端自动分配
      submitData.port = 0
    }

    // 移除前端特有的字段
    delete submitData.portConfig

    // 如果没有启用认证，清空用户名和密码
    if (!submitData.auth_required) {
      submitData.username = ''
      submitData.password = ''
    }
    
    const response = await createProxy(submitData)
    
    if (response.code === 200) {
      message.success('代理创建成功')
      emit('success')
    } else {
      message.error(response.msg || '代理创建失败')
    }
  } catch (error) {
    if (error.errorFields) {
      // 表单验证失败
      return
    }
    console.error('创建代理失败:', error)
    message.error('创建代理失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 24px 0 16px 0;
}

:deep(.ant-divider-inner-text) {
  font-weight: 600;
  color: #1890ff;
}
</style>
