import { get, post, put, del } from '@/utils/request'

/**
 * 获取客户端列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getClientList(params) {
  return post('/client/list', params)
}

/**
 * 获取单个客户端
 * @param {Number} id 客户端ID
 * @returns {Promise}
 */
export function getClient(id) {
  return get(`/client/${id}`)
}

/**
 * 更新客户端备注
 * @param {Object} data 客户端数据
 * @returns {Promise}
 */
export function updateClientRemark(data) {
  return put('/client', data)
}

/**
 * 删除客户端
 * @param {Number} id 客户端ID
 * @returns {Promise}
 */
export function deleteClient(id) {
  return del(`/client/${id}`)
}

/**
 * 发送命令到客户端
 * @param {Object} data 命令数据 {id, command}
 * @returns {Promise}
 */
export function sendCommand(data) {
  return post('/client/command', data)
}

/**
 * 断开客户端连接
 * @param {Object} data 客户端数据 {id}
 * @returns {Promise}
 */
export function disconnectClient(data) {
  return post('/client/disconnect', data)
}

/**
 * 清除所有离线客户端
 * @returns {Promise}
 */
export function clearOfflineClients() {
  return del('/client/offline')
}

/**
 * 发送客户端重连指令
 * @param {Number} id 客户端ID
 * @returns {Promise}
 */
export function sendClientReconnect(id) {
  return post(`/client/${id}/reconnect`)
}

/**
 * 发送客户端配置更新指令
 * @param {Number} id 客户端ID
 * @returns {Promise}
 */
export function sendClientConfigUpdate(id) {
  return post(`/client/${id}/config-update`)
}

/**
 * 执行客户端命令
 * @param {Number} id 客户端ID
 * @param {String} command 命令内容
 * @returns {Promise}
 */
export function executeClientCommand(id, command) {
  return post(`/client/${id}/execute-command`, { command })
}

/**
 * 获取心跳配置
 * @param {Number} clientId 客户端ID
 * @returns {Promise}
 */
export function getHeartbeatConfig(clientId) {
  return get(`/heartbeat/config?client_id=${clientId}`)
}

/**
 * 更新心跳配置
 * @param {Object} config 心跳配置对象
 * @returns {Promise}
 */
export function updateHeartbeatConfig(config) {
  return put('/heartbeat/config', config)
}

/**
 * 创建心跳配置
 * @param {Object} config 心跳配置对象
 * @returns {Promise}
 */
export function createHeartbeatConfig(config) {
  return post('/heartbeat/config', config)
}

// 导出API对象
export const clientApi = {
  getClientList,
  getClient,
  updateClientRemark,
  deleteClient,
  sendCommand,
  disconnectClient,
  clearOfflineClients
}