//go:build windows
// +build windows

package common

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/mem"
	psnet "github.com/shirou/gopsutil/v3/net"
)

// CreateHeartbeatPacket 创建心跳包
func (cm *ConnectionManager) CreateHeartbeatPacket(isPong bool) (*Packet, error) {
	var Code uint8
	if isPong {
		Code = PONG
	} else {
		Code = PING
	}
	packet := cm.CreatePacket([]byte("heartbeat"), Heartbeat, Code)
	// 加密数据包
	if err := packet.EncryptPacket(cm.metadata); err != nil {
		return nil, fmt.Errorf("加密心跳包失败: %v", err)
	}
	return packet, nil
}

// ClientHeartbeatRequest 客户端心跳请求结构体
type ClientHeartbeatRequest struct {
	ClientID    string              `json:"client_id"`
	Timestamp   time.Time           `json:"timestamp"`
	SequenceNum uint64              `json:"sequence_num"`
	SystemInfo  ClientSystemStatus  `json:"system_info"`
	NetworkInfo ClientNetworkStatus `json:"network_info"`
	Type        uint8               `json:"type"`
	Jitter      int                 `json:"jitter"`
}

// ClientSystemStatus 客户端系统状态
type ClientSystemStatus struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	DiskUsage   float64 `json:"disk_usage"`
	Uptime      int64   `json:"uptime"`
	LoadAvg     float64 `json:"load_avg"`
	OS          string  `json:"os"`
	Arch        string  `json:"arch"`
}

// ClientNetworkStatus 客户端网络状态
type ClientNetworkStatus struct {
	LocalIP    string  `json:"local_ip"`
	PublicIP   string  `json:"public_ip"`
	Latency    int64   `json:"latency"`
	PacketLoss float64 `json:"packet_loss"`
	Bandwidth  int64   `json:"bandwidth"`
}

// startClientHeartbeat 启动客户端主动心跳检测
func (cm *ConnectionManager) startClientHeartbeat(ctx context.Context) {
	// 心跳间隔：30秒 + 随机抖动
	baseInterval := 30 * time.Second

	// 添加±20%的随机抖动，避免所有客户端同时发送心跳
	jitter := time.Duration(float64(baseInterval) * 0.2 * (rand.Float64()*2 - 1))
	heartbeatInterval := baseInterval + jitter

	// 确保间隔不小于10秒
	if heartbeatInterval < 10*time.Second {
		heartbeatInterval = 10 * time.Second
	}

	log.Printf("客户端心跳检测已启动，间隔: %v", heartbeatInterval)

	ticker := time.NewTicker(heartbeatInterval)
	defer ticker.Stop()

	// 服务器响应超时计数
	var timeoutCount int32 = 0
	maxTimeoutCount := 3 // 最大超时3次就认为服务器离线

	for {
		select {
		case <-ctx.Done():
			log.Println("客户端心跳检测收到退出信号")
			return
		case <-ticker.C:
			// 发送心跳包到服务器
			go func() {
				if err := cm.sendClientHeartbeat(); err != nil {
					atomic.AddInt32(&timeoutCount, 1)
					currentCount := atomic.LoadInt32(&timeoutCount)
					log.Printf("发送客户端心跳失败 (%d/%d): %v", timeoutCount, maxTimeoutCount, err)

					if currentCount >= int32(maxTimeoutCount) {
						log.Printf("服务器连续%d次心跳超时，认为服务器离线，断开连接", maxTimeoutCount)
						// 触发重连机制
						if cm.conn != nil {
							cm.conn.Close()
						}
						return
					}
				} else {
					// 心跳发送成功，重置超时计数
					if atomic.LoadInt32(&timeoutCount) > 0 {
						log.Printf("心跳恢复正常，重置超时计数")
						atomic.StoreInt32(&timeoutCount, 0)
					}
					log.Println("客户端心跳发送成功")
				}
			}()
		}
	}
}

// HandleHeartbeatResponse 处理服务器的心跳响应（由包处理器调用）
func (cm *ConnectionManager) HandleHeartbeatResponse() {
	// 这个方法可以被包处理器调用，用于处理服务器的PONG响应
	// 目前只是记录日志，未来可以添加更复杂的逻辑
	log.Println("心跳响应处理完成")
}

// sendClientHeartbeat 发送结构化客户端心跳包
func (cm *ConnectionManager) sendClientHeartbeat() error {
	// 创建结构化心跳请求
	heartbeatReq := cm.createClientHeartbeatRequest()

	// 序列化心跳数据
	heartbeatData, err := json.Marshal(heartbeatReq)
	if err != nil {
		return fmt.Errorf("序列化心跳数据失败: %v", err)
	}

	// 创建TLV包
	heartbeatPacket, err := cm.createAdvancedHeartbeatPacket(heartbeatData, PING)
	if err != nil {
		return fmt.Errorf("创建心跳包失败: %v", err)
	}

	// 序列化并发送
	packetBytes := heartbeatPacket.Serialize()

	cm.mu.Lock()
	conn := cm.conn
	cm.mu.Unlock()

	if conn == nil {
		return fmt.Errorf("连接已断开")
	}

	if _, err := conn.Write(packetBytes); err != nil {
		return fmt.Errorf("发送心跳包失败: %v", err)
	}

	return nil
}

// createClientHeartbeatRequest 创建客户端心跳请求
func (cm *ConnectionManager) createClientHeartbeatRequest() *ClientHeartbeatRequest {
	return &ClientHeartbeatRequest{
		ClientID:    cm.getClientID(),
		Timestamp:   time.Now(),
		SequenceNum: cm.generateSequenceNumber(),
		SystemInfo:  cm.getClientSystemStatus(),
		NetworkInfo: cm.getClientNetworkStatus(),
		Type:        PING,
		Jitter:      cm.generateJitter(),
	}
}

// createAdvancedHeartbeatPacket 创建高级心跳包
func (cm *ConnectionManager) createAdvancedHeartbeatPacket(data []byte, heartbeatType uint8) (*Packet, error) {
	packet := &Packet{
		Header: &Header{
			Type:  Heartbeat,
			Code:  heartbeatType,
			Label: cm.generateLabel(),
		},
		PacketData: &PacketData{
			Data: data,
		},
	}

	// 加密数据包
	if err := packet.EncryptPacket(cm.metadata); err != nil {
		return nil, fmt.Errorf("加密心跳包失败: %v", err)
	}

	return packet, nil
}

// getClientID 获取客户端ID
func (cm *ConnectionManager) getClientID() string {
	// 可以使用MAC地址、主机名等生成唯一ID
	hostname, _ := os.Hostname()
	return fmt.Sprintf("%s_%d", hostname, time.Now().Unix())
}

// generateSequenceNumber 生成序列号
func (cm *ConnectionManager) generateSequenceNumber() uint64 {
	return uint64(time.Now().UnixNano())
}

// generateJitter 生成随机抖动值
func (cm *ConnectionManager) generateJitter() int {
	return rand.Intn(5000)
}

// generateLabel 生成标签
func (cm *ConnectionManager) generateLabel() uint32 {
	return uint32(time.Now().Unix())
}

// getClientSystemStatus 获取客户端系统状态（异步优化）
func (cm *ConnectionManager) getClientSystemStatus() ClientSystemStatus {
	var wg sync.WaitGroup
	var cpuUsage, memoryUsage, diskUsage float64
	var uptime int64

	// 异步获取各项系统信息
	wg.Add(4)

	// 获取CPU使用率
	go func() {
		defer wg.Done()
		cpuUsage = cm.getCPUUsage()
	}()

	// 获取内存使用率
	go func() {
		defer wg.Done()
		memoryUsage = cm.getMemoryUsage()
	}()

	// 获取磁盘使用率
	go func() {
		defer wg.Done()
		diskUsage = cm.getDiskUsage()
	}()

	// 获取系统运行时间
	go func() {
		defer wg.Done()
		uptime = cm.getUptime()
	}()

	// 等待所有goroutine完成
	wg.Wait()

	return ClientSystemStatus{
		CPUUsage:    cpuUsage,
		MemoryUsage: memoryUsage,
		DiskUsage:   diskUsage,
		Uptime:      uptime,
		LoadAvg:     cpuUsage / 100.0 * 4.0, // Windows用CPU使用率模拟负载 (0-4.0)
		OS:          runtime.GOOS,
		Arch:        runtime.GOARCH,
	}
}

// 网络测试缓存
var (
	lastNetworkTest     time.Time
	cachedNetworkStatus ClientNetworkStatus
	networkTestMutex    sync.Mutex
)

// getClientNetworkStatus 获取客户端网络状态（异步优化 + 缓存）
func (cm *ConnectionManager) getClientNetworkStatus() ClientNetworkStatus {
	networkTestMutex.Lock()
	defer networkTestMutex.Unlock()

	// 如果距离上次网络测试不到5分钟，返回缓存结果
	if time.Since(lastNetworkTest) < 5*time.Minute && cachedNetworkStatus.LocalIP != "" {
		return cachedNetworkStatus
	}

	var wg sync.WaitGroup
	var localIP, publicIP string
	var latency int64
	var packetLoss float64
	var bandwidth int64

	// 异步获取网络信息
	wg.Add(4) // 减少并发数，移除频繁的网络测试

	// 获取本地IP
	go func() {
		defer wg.Done()
		localIP = cm.getLocalIP()
	}()

	// 获取公网IP（仅在缓存为空时）
	go func() {
		defer wg.Done()
		if cachedNetworkStatus.PublicIP == "" {
			publicIP = cm.getPublicIP()
		} else {
			publicIP = cachedNetworkStatus.PublicIP
		}
	}()

	// 获取到服务器的延迟和丢包率（降低频率）
	go func() {
		defer wg.Done()
		latency, packetLoss = cm.pingServerWithStats()
	}()

	go func() {
		defer wg.Done()
		bandwidth = cm.measureBandwidth()

	}()

	// 等待所有goroutine完成
	wg.Wait()

	// 更新缓存
	lastNetworkTest = time.Now()
	cachedNetworkStatus = ClientNetworkStatus{
		LocalIP:    localIP,
		PublicIP:   publicIP,
		Latency:    latency,
		PacketLoss: packetLoss,
		Bandwidth:  bandwidth,
	}

	return cachedNetworkStatus
}

// getCPUUsage 获取真实CPU使用率
func (cm *ConnectionManager) getCPUUsage() float64 {
	// 使用gopsutil获取CPU使用率
	percent, err := cpu.Percent(time.Second, false)
	if err != nil || len(percent) == 0 {
		log.Printf("获取CPU使用率失败: %v", err)
		return 0.0
	}
	return percent[0]
}

// getMemoryUsage 获取真实内存使用率
func (cm *ConnectionManager) getMemoryUsage() float64 {
	// 使用gopsutil获取内存信息
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("获取内存信息失败: %v", err)
		return 0.0
	}
	return memInfo.UsedPercent
}

// getDiskUsage 获取真实磁盘使用率
func (cm *ConnectionManager) getDiskUsage() float64 {
	// 使用gopsutil获取C盘使用率
	diskInfo, err := disk.Usage("C:")
	if err != nil {
		log.Printf("获取磁盘信息失败: %v", err)
		return 0.0
	}
	return diskInfo.UsedPercent
}

// getUptime 获取真实系统运行时间
func (cm *ConnectionManager) getUptime() int64 {
	// 使用gopsutil获取系统启动时间
	bootTime, err := host.BootTime()
	if err != nil {
		log.Printf("获取系统启动时间失败: %v", err)
		return 0
	}
	// 返回运行时间（秒）
	return int64(time.Now().Unix() - int64(bootTime))
}

// getLocalIP 获取真实的本地IP地址
func (cm *ConnectionManager) getLocalIP() string {
	// 获取本机的外网IP地址（连接到服务器时使用的IP）
	if cm.conn != nil {
		if localAddr := cm.conn.LocalAddr(); localAddr != nil {
			if tcpAddr, ok := localAddr.(*net.TCPAddr); ok {
				return tcpAddr.IP.String()
			}
		}
	}

	// 备用方法：获取默认网卡的IP
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	if localAddr := conn.LocalAddr(); localAddr != nil {
		if udpAddr, ok := localAddr.(*net.UDPAddr); ok {
			return udpAddr.IP.String()
		}
	}

	return "127.0.0.1"
}

// getPublicIP 获取公网IP地址
func (cm *ConnectionManager) getPublicIP() string {
	// 使用最简单可靠的服务
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", "http://www.bt.cn/api/getipaddress", nil)
	if err != nil {
		return "unknown"
	}

	client := &http.Client{Timeout: 3 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "unknown"
	}
	defer resp.Body.Close()

	ipBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "unknown"
	}
	return strings.TrimSpace(string(ipBytes))
}

// pingServerWithStats 使用ICMP ping获取到服务器的延迟和丢包率
func (cm *ConnectionManager) pingServerWithStats() (int64, float64) {
	if cm.conn == nil {
		return 0, 0.0
	}

	serverAddr := cm.conn.RemoteAddr()
	if serverAddr == nil {
		return 0, 0.0
	}

	tcpAddr, ok := serverAddr.(*net.TCPAddr)
	if !ok {
		return 0, 0.0
	}

	// 使用goroutine异步执行ping，避免阻塞
	resultChan := make(chan struct {
		latency int64
		loss    float64
	}, 1)

	go func() {
		latency, loss := cm.icmpPing(tcpAddr.IP.String())
		resultChan <- struct {
			latency int64
			loss    float64
		}{latency, loss}
	}()

	// 3秒超时
	select {
	case result := <-resultChan:
		return result.latency, result.loss
	case <-time.After(3 * time.Second):
		log.Printf("Ping操作超时，使用默认值")
		return 0, 0.0
	}
}

// icmpPing 优先使用系统ping命令，失败则回退到TCP ping
func (cm *ConnectionManager) icmpPing(host string) (int64, float64) {
	// 首先尝试使用系统ping命令
	latency, packetLoss := cm.systemPing(host)
	if latency > 0 || packetLoss < 100.0 {
		log.Printf("系统ping成功: %s, 延迟=%dms, 丢包率=%.1f%%", host, latency, packetLoss)
		return latency, packetLoss
	}

	// 如果系统ping失败，回退到TCP连接测试
	log.Printf("系统ping失败，使用TCP连接测试: %s", host)
	return cm.tcpPingFallback(host)
}

// systemPing 使用Windows系统ping命令
func (cm *ConnectionManager) systemPing(host string) (int64, float64) {
	// Windows ping命令：ping -n 3 -w 2000 host
	// -n 3: 发送3个包
	// -w 2000: 超时2秒
	cmd := exec.Command("ping", "-n", "3", "-w", "2000", host)
	output, err := cmd.Output()
	if err != nil {
		log.Printf("执行ping命令失败: %v", err)
		return 0, 100.0
	}

	return cm.parseWindowsPingOutput(string(output))
}

// parseWindowsPingOutput 解析Windows ping命令的输出
func (cm *ConnectionManager) parseWindowsPingOutput(output string) (int64, float64) {
	lines := strings.Split(output, "\n")
	var totalLatency int64
	var successCount int
	var totalCount int

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 查找包含延迟信息的行，例如：
		// "Reply from ***********: bytes=32 time=1ms TTL=64"
		if strings.Contains(line, "Reply from") && strings.Contains(line, "time=") {
			successCount++
			totalCount++

			// 提取延迟时间
			if timeIndex := strings.Index(line, "time="); timeIndex != -1 {
				timeStr := line[timeIndex+5:]
				if msIndex := strings.Index(timeStr, "ms"); msIndex != -1 {
					timeStr = timeStr[:msIndex]
					// 处理 "time<1ms" 的情况
					if strings.HasPrefix(timeStr, "<") {
						timeStr = "1"
					}
					if latency, err := strconv.ParseInt(timeStr, 10, 64); err == nil {
						totalLatency += latency
					}
				}
			}
		} else if strings.Contains(line, "Request timed out") || strings.Contains(line, "请求超时") {
			totalCount++
		}
	}

	if totalCount == 0 {
		// 尝试从统计信息中获取包数量
		for _, line := range lines {
			if strings.Contains(line, "Packets: Sent =") || strings.Contains(line, "数据包: 已发送 =") {
				// 解析统计行，例如：
				// "Packets: Sent = 3, Received = 2, Lost = 1 (33% loss)"
				totalCount = 3 // 默认发送3个包
				break
			}
		}
		if totalCount == 0 {
			return 0, 100.0
		}
	}

	var avgLatency int64
	if successCount > 0 {
		avgLatency = totalLatency / int64(successCount)
	}

	packetLoss := float64(totalCount-successCount) / float64(totalCount) * 100.0

	return avgLatency, packetLoss
}

// tcpPingFallback TCP连接测试作为ICMP的回退方案
func (cm *ConnectionManager) tcpPingFallback(host string) (int64, float64) {
	// 获取服务器端口
	serverAddr := cm.conn.RemoteAddr()
	if serverAddr == nil {
		return 0, 100.0
	}

	tcpAddr, ok := serverAddr.(*net.TCPAddr)
	if !ok {
		return 0, 100.0
	}

	const pingCount = 3
	var totalLatency int64
	var successCount int

	for i := 0; i < pingCount; i++ {
		start := time.Now()
		conn, err := net.DialTimeout("tcp", host+":"+fmt.Sprintf("%d", tcpAddr.Port), 2*time.Second)
		if err != nil {
			continue
		}
		conn.Close()

		latency := time.Since(start).Milliseconds()
		totalLatency += latency
		successCount++

		time.Sleep(200 * time.Millisecond)
	}

	if successCount == 0 {
		return 0, 100.0
	}

	avgLatency := totalLatency / int64(successCount)
	packetLoss := float64(pingCount-successCount) / float64(pingCount) * 100.0

	return avgLatency, packetLoss
}

// measureBandwidth 获取网卡流量信息（当前带宽使用情况）
func (cm *ConnectionManager) measureBandwidth() int64 {
	// 获取网络接口统计信息
	interfaces, err := psnet.IOCounters(true)
	if err != nil {
		log.Printf("获取网络接口信息失败: %v", err)
		return 0
	}

	var totalBytesRecv, totalBytesSent uint64
	var activeInterfaces int

	// 遍历所有网络接口
	for _, iface := range interfaces {
		// 跳过回环接口和无流量的接口
		if iface.Name == "lo" || iface.Name == "Loopback" ||
			strings.Contains(strings.ToLower(iface.Name), "loopback") {
			continue
		}

		// 只统计有流量的接口
		if iface.BytesRecv > 0 || iface.BytesSent > 0 {
			totalBytesRecv += iface.BytesRecv
			totalBytesSent += iface.BytesSent
			activeInterfaces++

			log.Printf("网卡 %s: 接收=%d bytes, 发送=%d bytes",
				iface.Name, iface.BytesRecv, iface.BytesSent)
		}
	}

	if activeInterfaces == 0 {
		return 0
	}

	// 🚀 不再虚构网络带宽数据，返回真实的0值表示心跳检测不提供带宽信息
	// 真实的网络速度应该通过专门的网络监控API获取

	log.Printf("网络流量统计: 总接收=%d bytes, 总发送=%d bytes, 活跃接口=%d",
		totalBytesRecv, totalBytesSent, activeInterfaces)

	// 返回0表示心跳检测不提供带宽数据，避免虚构数据
	return 0
}

// parseServerHeartbeatResponse 解析服务器心跳响应
func (cm *ConnectionManager) parseServerHeartbeatResponse(data []byte) {
	// 定义服务器响应结构体（简化版）
	type ServerHeartbeatResponse struct {
		ServerID    string    `json:"server_id"`
		Timestamp   time.Time `json:"timestamp"`
		SequenceNum uint64    `json:"sequence_num"`
		ServerInfo  struct {
			Status    uint8  `json:"status"`
			Timestamp int64  `json:"timestamp"`
			Version   string `json:"version"`
		} `json:"server_info"`
		ClientInfo struct {
			ShouldReconnect bool     `json:"should_reconnect"`
			NewServerAddr   string   `json:"new_server_addr"`
			ConfigUpdate    bool     `json:"config_update"`
			Commands        []string `json:"commands"`
		} `json:"client_info"`
		Config struct {
			Interval    int `json:"interval"`
			Timeout     int `json:"timeout"`
			MaxRetries  int `json:"max_retries"`
			JitterRange int `json:"jitter_range"`
		} `json:"config"`
		Type   uint8 `json:"type"`
		Jitter int   `json:"jitter"`
	}

	var serverResp ServerHeartbeatResponse
	if err := json.Unmarshal(data, &serverResp); err != nil {
		log.Printf("解析服务器心跳响应失败: %v", err)
		return
	}

	log.Printf("收到服务器心跳响应: ServerID=%s, Status=%d, Version=%s",
		serverResp.ServerID, serverResp.ServerInfo.Status, serverResp.ServerInfo.Version)

	// 处理服务器指令
	if serverResp.ClientInfo.ShouldReconnect {
		log.Printf("服务器要求重连到: %s", serverResp.ClientInfo.NewServerAddr)
		// TODO: 实现重连逻辑
	}

	if serverResp.ClientInfo.ConfigUpdate {
		log.Println("服务器要求更新配置")
		// TODO: 实现配置更新逻辑
	}

	if len(serverResp.ClientInfo.Commands) > 0 {
		log.Printf("服务器发送了%d个命令", len(serverResp.ClientInfo.Commands))
		// TODO: 实现命令执行逻辑
	}
}
