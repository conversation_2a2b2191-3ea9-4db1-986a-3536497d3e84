//go:build windows
// +build windows

package common

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"
	"unsafe"

	psutilNet "github.com/shirou/gopsutil/v3/net"
	"github.com/shirou/gopsutil/v3/process"
)

// 网络监控并发控制
var (
	networkMutex        sync.Mutex // 网络监控请求互斥锁
	isNetworkProcessing bool       // 是否正在处理网络监控请求
)

// 网络连接数量限制
const (
	MaxNetworkConnections = 1000 // 最大返回连接数
)

// Windows API 相关定义
var (
	iphlpapi        = syscall.NewLazyDLL("iphlpapi.dll")
	procGetTcpTable = iphlpapi.NewProc("GetTcpTable")
	procGetUdpTable = iphlpapi.NewProc("GetUdpTable")
	procGetIfTable  = iphlpapi.NewProc("GetIfTable")
	procSetTcpEntry = iphlpapi.NewProc("SetTcpEntry")
)

// 网络监控请求结构体
type NetworkStatsRequest struct {
	TaskID uint64 `json:"task_id"`
}

type NetworkInterfacesRequest struct {
	TaskID uint64 `json:"task_id"`
}

type NetworkConnectionsRequest struct {
	TaskID   uint64 `json:"task_id"`
	Protocol string `json:"protocol"`
	State    string `json:"state"`
}

type CloseConnectionRequest struct {
	TaskID       uint64 `json:"task_id"`
	ConnectionID string `json:"connection_id"`
	LocalAddr    string `json:"local_addr"`
	LocalPort    int    `json:"local_port"`
	RemoteAddr   string `json:"remote_addr"`
	RemotePort   int    `json:"remote_port"`
	Protocol     string `json:"protocol"`
}

type NetworkStats struct {
	UploadSpeed       float64 `json:"upload_speed"`
	DownloadSpeed     float64 `json:"download_speed"`
	ActiveConnections int     `json:"active_connections"`
	PacketLoss        float64 `json:"packet_loss"`
	TotalBytesSent    uint64  `json:"total_bytes_sent"`
	TotalBytesRecv    uint64  `json:"total_bytes_recv"`
	Timestamp         int64   `json:"timestamp"`
}

type NetInterface struct {
	Name          string `json:"name"`
	Type          string `json:"type"`
	Status        string `json:"status"`
	IPAddress     string `json:"ip_address"`
	MACAddress    string `json:"mac_address"`
	Speed         uint64 `json:"speed"`
	BytesSent     uint64 `json:"bytes_sent"`
	BytesReceived uint64 `json:"bytes_received"`
	PacketsSent   uint64 `json:"packets_sent"`
	PacketsRecv   uint64 `json:"packets_recv"`
	ErrorsIn      uint64 `json:"errors_in"`
	ErrorsOut     uint64 `json:"errors_out"`
	DropsIn       uint64 `json:"drops_in"`
	DropsOut      uint64 `json:"drops_out"`
}

type NetworkConnection struct {
	ID              string    `json:"id"`
	Protocol        string    `json:"protocol"`
	LocalAddress    string    `json:"local_address"`
	LocalPort       int       `json:"local_port"`
	RemoteAddress   string    `json:"remote_address"`
	RemotePort      int       `json:"remote_port"`
	State           string    `json:"state"`
	ProcessName     string    `json:"process_name"`
	PID             int       `json:"pid"`
	EstablishedTime time.Time `json:"established_time"`
	Duration        string    `json:"duration"`
	BytesSent       uint64    `json:"bytes_sent"`
	BytesReceived   uint64    `json:"bytes_received"`
}

// 响应结构体
type NetworkStatsResponse struct {
	TaskID  uint64       `json:"task_id"`
	Success bool         `json:"success"`
	Message string       `json:"message"`
	Error   string       `json:"error"`
	Stats   NetworkStats `json:"stats"`
}

type NetworkInterfacesResponse struct {
	TaskID     uint64         `json:"task_id"`
	Success    bool           `json:"success"`
	Error      string         `json:"error"`
	Interfaces []NetInterface `json:"interfaces"`
}

type NetworkConnectionsResponse struct {
	TaskID      uint64              `json:"task_id"`
	Success     bool                `json:"success"`
	Message     string              `json:"message"`
	Error       string              `json:"error"`
	Connections []NetworkConnection `json:"connections"`
	Total       int                 `json:"total"`
}

type CloseConnectionResponse struct {
	TaskID  uint64 `json:"task_id"`
	Success bool   `json:"success"`
	Message string `json:"message"`
	Error   string `json:"error"`
}

// handleNetworkRequest 处理网络监控请求的主入口
func (cm *ConnectionManager) handleNetworkRequest(packet *Packet) {
	// 检查是否有其他网络监控请求正在处理
	networkMutex.Lock()
	if isNetworkProcessing {
		networkMutex.Unlock()
		log.Printf("⚠️ 网络监控请求正在处理中，跳过当前请求: Code=%d", packet.Header.Code)
		// 发送忙碌响应
		cm.sendBusyResponse(packet.Header.Code)
		return
	}
	isNetworkProcessing = true
	networkMutex.Unlock()

	// 处理完成后释放锁
	defer func() {
		networkMutex.Lock()
		isNetworkProcessing = false
		networkMutex.Unlock()
	}()

	switch packet.Header.Code {
	case NetStatsCmd:
		cm.handleNetworkStats(packet)
	case NetInterfacesCmd:
		cm.handleNetworkInterfaces(packet)
	case NetConnectionsCmd:
		cm.handleNetworkConnections(packet)
	case NetCloseConnCmd:
		cm.handleCloseConnection(packet)
	default:
		log.Printf("未知的网络监控操作代码: %d", packet.Header.Code)
	}
}

// handleNetworkStats 处理网络统计信息请求
func (cm *ConnectionManager) handleNetworkStats(packet *Packet) {
	log.Printf("🌐 开始处理网络统计信息请求")

	errorResp := NetworkStatsResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req NetworkStatsRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络统计信息请求失败: %v", err)
		cm.sendNetworkResponse(NetStatsCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	stats, err := cm.getNetworkStats()
	if err != nil {
		log.Printf("❌ 获取网络统计信息失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendNetworkResponse(NetStatsCmd, errorResp)
		return
	}

	response := NetworkStatsResponse{
		TaskID:  req.TaskID,
		Success: true,
		Stats:   *stats,
	}

	cm.sendNetworkResponse(NetStatsCmd, response)
	log.Printf("✅ 网络统计信息请求处理完成")
}

// handleNetworkInterfaces 处理网络接口信息请求
func (cm *ConnectionManager) handleNetworkInterfaces(packet *Packet) {
	log.Printf("🌐 开始处理网络接口信息请求")

	errorResp := NetworkInterfacesResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req NetworkInterfacesRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络接口信息请求失败: %v", err)
		cm.sendNetworkResponse(NetInterfacesCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	interfaces, err := cm.getNetworkInterfaces()
	if err != nil {
		log.Printf("❌ 获取网络接口信息失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendNetworkResponse(NetInterfacesCmd, errorResp)
		return
	}

	response := NetworkInterfacesResponse{
		TaskID:     req.TaskID,
		Success:    true,
		Interfaces: interfaces,
	}

	cm.sendNetworkResponse(NetInterfacesCmd, response)
	log.Printf("✅ 网络接口信息请求处理完成")
}

// handleNetworkConnections 处理网络连接信息请求
func (cm *ConnectionManager) handleNetworkConnections(packet *Packet) {
	log.Printf("🌐 开始处理网络连接信息请求")

	errorResp := NetworkConnectionsResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req NetworkConnectionsRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络连接信息请求失败: %v", err)
		cm.sendNetworkResponse(NetConnectionsCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	connections, err := cm.getNetworkConnections(req.Protocol, req.State)
	if err != nil {
		log.Printf("❌ 获取网络连接信息失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendNetworkResponse(NetConnectionsCmd, errorResp)
		return
	}

	response := NetworkConnectionsResponse{
		TaskID:      req.TaskID,
		Success:     true,
		Connections: connections,
		Total:       len(connections),
	}

	cm.sendNetworkResponse(NetConnectionsCmd, response)
	log.Printf("✅ 网络连接信息请求处理完成")
}

// handleCloseConnection 处理关闭网络连接请求
func (cm *ConnectionManager) handleCloseConnection(packet *Packet) {
	log.Printf("🌐 开始处理关闭网络连接请求")

	errorResp := CloseConnectionResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req CloseConnectionRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析关闭网络连接请求失败: %v", err)
		cm.sendNetworkResponse(NetCloseConnCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	err := cm.closeNetworkConnection(req.ConnectionID, req.Protocol, req.LocalAddr, req.LocalPort, req.RemoteAddr, req.RemotePort)
	if err != nil {
		log.Printf("❌ 关闭网络连接失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendNetworkResponse(NetCloseConnCmd, errorResp)
		return
	}

	response := CloseConnectionResponse{
		TaskID:  req.TaskID,
		Success: true,
		Message: "网络连接关闭成功",
	}

	cm.sendNetworkResponse(NetCloseConnCmd, response)
	log.Printf("✅ 关闭网络连接请求处理完成")
}

// sendNetworkResponse 发送网络监控响应
func (cm *ConnectionManager) sendNetworkResponse(code uint8, response interface{}) {
	cm.sendResp(Network, code, response)
}

// sendBusyResponse 发送忙碌响应
func (cm *ConnectionManager) sendBusyResponse(code uint8) {
	var response interface{}

	switch code {
	case NetStatsCmd:
		response = NetworkStatsResponse{
			TaskID:  0,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetInterfacesCmd:
		response = NetworkInterfacesResponse{
			TaskID:  0,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetConnectionsCmd:
		response = NetworkConnectionsResponse{
			TaskID:  0,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetCloseConnCmd:
		response = CloseConnectionResponse{
			TaskID:  0,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	default:
		return
	}

	cm.sendNetworkResponse(code, response)
}

// getNetworkStats 获取网络统计信息
func (cm *ConnectionManager) getNetworkStats() (*NetworkStats, error) {
	stats := &NetworkStats{
		Timestamp: time.Now().Unix(),
	}

	// 使用 netstat 命令获取网络统计信息
	cmd := exec.Command("netstat", "-e")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行netstat命令失败: %v", err)
	}

	// 解析netstat输出
	lines := strings.Split(string(output), "\n")
	for i, line := range lines {
		if strings.Contains(line, "Bytes") && i+1 < len(lines) {
			parts := strings.Fields(lines[i+1])
			if len(parts) >= 2 {
				if recv, err := strconv.ParseUint(parts[0], 10, 64); err == nil {
					stats.TotalBytesRecv = recv
				}
				if sent, err := strconv.ParseUint(parts[1], 10, 64); err == nil {
					stats.TotalBytesSent = sent
				}
			}
			break
		}
	}

	// 获取活跃连接数
	connections, err := cm.getNetworkConnections("ALL", "")
	if err == nil {
		stats.ActiveConnections = len(connections)
	}

	// 🚀 使用真实的速度计算方法（学习dashboard实现）
	// 优先使用gopsutil获取真实的网络统计数据
	realSent, realRecv, err := cm.getRealNetworkStats()
	if err != nil {
		log.Printf("⚠️ 获取真实网络统计失败，使用默认数据: %v", err)
		realSent, realRecv = stats.TotalBytesSent, stats.TotalBytesRecv
	}

	uploadSpeed, downloadSpeed := cm.calculateRealNetworkSpeed(realSent, realRecv)
	stats.UploadSpeed = uploadSpeed
	stats.DownloadSpeed = downloadSpeed
	stats.TotalBytesSent = realSent
	stats.TotalBytesRecv = realRecv

	return stats, nil
}

// getNetworkInterfaces 获取网络接口信息（流式版本）
func (cm *ConnectionManager) getNetworkInterfaces() ([]NetInterface, error) {
	log.Printf("🔍 getNetworkInterfaces: 开始获取网络接口信息")

	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("获取网络接口失败: %v", err)
	}

	log.Printf("📊 getNetworkInterfaces: 找到%d个网络接口", len(interfaces))
	var result []NetInterface

	for i, iface := range interfaces {
		log.Printf("🌐 getNetworkInterfaces: 处理接口 %d/%d: %s", i+1, len(interfaces), iface.Name)

		netIface := NetInterface{
			Name:       iface.Name,
			MACAddress: iface.HardwareAddr.String(),
		}

		// 获取接口状态
		if iface.Flags&net.FlagUp != 0 {
			netIface.Status = "Up"
		} else {
			netIface.Status = "Down"
		}

		// 获取接口类型
		if iface.Flags&net.FlagLoopback != 0 {
			netIface.Type = "Loopback"
		} else if strings.Contains(strings.ToLower(iface.Name), "ethernet") {
			netIface.Type = "Ethernet"
		} else if strings.Contains(strings.ToLower(iface.Name), "wireless") || strings.Contains(strings.ToLower(iface.Name), "wifi") {
			netIface.Type = "Wireless"
		} else {
			netIface.Type = "Other"
		}

		// 获取IP地址
		addrs, err := iface.Addrs()
		if err == nil {
			for _, addr := range addrs {
				if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
					if ipnet.IP.To4() != nil {
						netIface.IPAddress = ipnet.IP.String()
						break
					}
				}
			}
		}

		// 🚀 获取真实的接口统计数据
		log.Printf("🔍 getNetworkInterfaces: 开始获取接口%s的统计数据", netIface.Name)
		cm.getWindowsInterfaceStats(&netIface)
		log.Printf("🔍 getNetworkInterfaces: 接口%s统计数据获取完成 - 发送:%d, 接收:%d",
			netIface.Name, netIface.BytesSent, netIface.BytesReceived)

		result = append(result, netIface)

		// 🚀 流式传输：每处理完一个接口就发送一次进度更新
		if i < len(interfaces)-1 { // 不是最后一个接口
			cm.sendInterfaceProgress(i+1, len(interfaces), netIface)
		}
	}

	log.Printf("✅ getNetworkInterfaces: 网络接口信息获取完成，共%d个接口", len(result))
	return result, nil
}

// sendInterfaceProgress 发送接口处理进度
func (cm *ConnectionManager) sendInterfaceProgress(current, total int, iface NetInterface) {
	log.Printf("📤 发送接口进度: %d/%d - %s", current, total, iface.Name)

	progressData := map[string]interface{}{
		"type":      "interface_progress",
		"current":   current,
		"total":     total,
		"progress":  float64(current) / float64(total) * 100,
		"interface": iface,
		"message":   fmt.Sprintf("正在处理接口 %s (%d/%d)", iface.Name, current, total),
	}

	// 发送进度数据包
	cm.sendProgressResponse(NetProgressCmd, progressData)
}

// sendProgressResponse 发送进度响应
func (cm *ConnectionManager) sendProgressResponse(code uint8, data interface{}) {
	log.Printf("📤 sendProgressResponse: 发送进度数据")

	// 添加调试日志：打印即将发送的进度数据
	if jsonData, err := json.Marshal(data); err == nil {
		log.Printf("📤 sendProgressResponse: 进度数据JSON: %s", string(jsonData))
	} else {
		log.Printf("❌ 序列化进度数据失败: %v", err)
		return
	}

	log.Printf("📤 sendProgressResponse: 调用 sendResp...")
	// 🔧 修复：直接传递原始数据，让sendResp进行序列化，避免双重序列化
	cm.sendResp(Network, code, data)
}

// getWindowsInterfaceStats 获取Windows接口的真实统计数据并计算速度
func (cm *ConnectionManager) getWindowsInterfaceStats(iface *NetInterface) {
	log.Printf("🔧 getWindowsInterfaceStats: 获取接口%s的统计数据", iface.Name)
	log.Printf("🔍 getWindowsInterfaceStats: 接口初始状态 - 发送:%d, 接收:%d", iface.BytesSent, iface.BytesReceived)

	// 🚀 方法1：优先使用gopsutil获取统计数据（最可靠）
	if cm.getStatsFromPsutilWindows(iface) {
		log.Printf("✅ getWindowsInterfaceStats: 通过gopsutil成功获取接口%s统计", iface.Name)
		// 计算实时速度
		cm.calculateInterfaceSpeedWindows(iface)
		return
	}

	// 🚀 方法2：尝试使用netsh命令获取接口统计
	if cm.getStatsFromNetsh(iface) {
		log.Printf("✅ getWindowsInterfaceStats: 通过netsh成功获取接口%s统计", iface.Name)
		// 计算实时速度
		cm.calculateInterfaceSpeedWindows(iface)
		return
	}

	// 🚀 方法3：尝试使用wmic命令获取网络适配器统计
	if cm.getStatsFromWmic(iface) {
		log.Printf("✅ getWindowsInterfaceStats: 通过wmic成功获取接口%s统计", iface.Name)
		// 计算实时速度
		cm.calculateInterfaceSpeedWindows(iface)
		return
	}

	// 如果所有方法都失败，保持原始的0值（真实的无数据状态）
	log.Printf("⚠️ getWindowsInterfaceStats: 无法获取接口%s的统计数据，保持0值", iface.Name)
}

// getStatsFromNetsh 使用netsh命令获取接口统计
func (cm *ConnectionManager) getStatsFromNetsh(iface *NetInterface) bool {
	log.Printf("🔧 getStatsFromNetsh: 尝试通过netsh获取接口%s统计", iface.Name)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用netsh interface ipv4 show interfaces命令
	cmd := exec.CommandContext(ctx, "netsh", "interface", "ipv4", "show", "interfaces")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("⚠️ getStatsFromNetsh: netsh命令失败: %v", err)
		return false
	}

	outputStr := string(output)
	log.Printf("📊 getStatsFromNetsh: netsh输出长度: %d", len(outputStr))

	// 解析netsh输出（这个命令主要显示接口状态，不包含详细统计）
	// 如果需要详细统计，可能需要使用其他netsh命令或WMI

	return false // netsh interface命令通常不包含字节统计
}

// getStatsFromWmic 使用wmic命令获取网络适配器统计
func (cm *ConnectionManager) getStatsFromWmic(iface *NetInterface) bool {
	log.Printf("🔧 getStatsFromWmic: 尝试通过wmic获取接口%s统计", iface.Name)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用wmic查询网络适配器统计
	cmd := exec.CommandContext(ctx, "wmic", "path", "Win32_PerfRawData_Tcpip_NetworkInterface",
		"get", "Name,BytesReceivedPerSec,BytesSentPerSec", "/format:csv")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("⚠️ getStatsFromWmic: wmic命令失败: %v", err)
		return false
	}

	outputStr := string(output)
	lines := strings.Split(outputStr, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "Node") {
			continue
		}

		fields := strings.Split(line, ",")
		if len(fields) >= 4 {
			name := strings.TrimSpace(fields[3])
			// Windows接口名称可能包含特殊字符，需要模糊匹配
			if strings.Contains(name, iface.Name) || strings.Contains(iface.Name, name) {
				// 解析字节统计（注意：这些可能是累计值或速率值）
				if bytesRecv := strings.TrimSpace(fields[1]); bytesRecv != "" {
					if val, err := strconv.ParseUint(bytesRecv, 10, 64); err == nil {
						iface.BytesReceived = val
					}
				}
				if bytesSent := strings.TrimSpace(fields[2]); bytesSent != "" {
					if val, err := strconv.ParseUint(bytesSent, 10, 64); err == nil {
						iface.BytesSent = val
					}
				}

				log.Printf("✅ getStatsFromWmic: 接口%s统计 - 接收:%d, 发送:%d",
					iface.Name, iface.BytesReceived, iface.BytesSent)
				return true
			}
		}
	}

	return false
}

// Windows网络统计缓存变量（用于计算速度）
var (
	windowsLastNetworkStat      time.Time
	windowsPrevBytesSent        uint64
	windowsPrevBytesRecv        uint64
	windowsIsNetworkInitialized bool
	windowsLastUploadKbps       float64
	windowsLastDownloadKbps     float64
	windowsNetworkMutex         sync.Mutex
)

// getStatsFromPsutil 使用gopsutil库获取网络统计（学习dashboard实现）
func (cm *ConnectionManager) getStatsFromPsutil() (uint64, uint64, error) {
	log.Printf("🔧 getStatsFromPsutil: 使用gopsutil获取网络统计")

	// 🚀 完全学习dashboard的实现方法
	// 注意：您需要导入 psutilNet "github.com/shirou/gopsutil/v3/net"
	stats, err := psutilNet.IOCounters(true) // 获取每个接口的详细信息
	if err != nil || len(stats) == 0 {
		return 0, 0, fmt.Errorf("gopsutil获取网络统计失败: %v", err)
	}

	log.Printf("📊 getStatsFromPsutil: 找到%d个网络接口", len(stats))

	// 找到流量最大的有效接口（学习dashboard逻辑）
	var maxTrafficInterface *psutilNet.IOCountersStat
	var maxTraffic uint64

	for i := range stats {
		stat := &stats[i]
		if !cm.isValidNetworkInterface(stat.Name) {
			continue
		}

		// 计算总流量（上传+下载）
		totalTraffic := stat.BytesSent + stat.BytesRecv
		if totalTraffic > maxTraffic {
			maxTraffic = totalTraffic
			maxTrafficInterface = stat
			log.Printf("📊 getStatsFromPsutil: 发现更大流量接口 %s: 发送=%d, 接收=%d",
				stat.Name, stat.BytesSent, stat.BytesRecv)
		}
	}

	// 如果找到有效接口，使用该接口的数据
	if maxTrafficInterface != nil {
		log.Printf("✅ getStatsFromPsutil: 使用最大流量接口 %s", maxTrafficInterface.Name)
		return maxTrafficInterface.BytesSent, maxTrafficInterface.BytesRecv, nil
	}

	// 如果没有找到有效接口，回退到汇总所有非回环接口
	var totalSent, totalRecv uint64
	for _, stat := range stats {
		if !strings.Contains(strings.ToLower(stat.Name), "loopback") &&
			!strings.HasPrefix(stat.Name, "lo") {
			totalSent += stat.BytesSent
			totalRecv += stat.BytesRecv
			log.Printf("📊 getStatsFromPsutil: 汇总接口 %s: 发送=%d, 接收=%d",
				stat.Name, stat.BytesSent, stat.BytesRecv)
		}
	}

	log.Printf("✅ getStatsFromPsutil: 汇总统计 - 总发送=%d, 总接收=%d", totalSent, totalRecv)
	return totalSent, totalRecv, nil
}

// isValidNetworkInterface 检查是否为有效的网络接口（完全学习dashboard实现）
func (cm *ConnectionManager) isValidNetworkInterface(name string) bool {
	// 🚀 完全学习dashboard的接口过滤逻辑
	excludedPrefixes := []string{
		"lo",       // 回环接口
		"Loopback", // Windows回环接口
		"utun",     // macOS隧道接口
		"awdl",     // Apple Wireless Direct Link
		"llw",      // Low Latency WLAN
		"bridge",   // 桥接接口
		"vmenet",   // VMware虚拟网络接口
		"vnic",     // 虚拟网卡
		"docker",   // Docker接口
		"veth",     // 虚拟以太网接口
		"tap",      // TAP接口
		"tun",      // TUN接口
		"ap",       // 接入点接口
	}

	for _, prefix := range excludedPrefixes {
		if strings.HasPrefix(strings.ToLower(name), strings.ToLower(prefix)) {
			return false
		}
	}

	// Windows特定的额外过滤
	nameLower := strings.ToLower(name)
	if strings.Contains(nameLower, "virtual") ||
		strings.Contains(nameLower, "vmware") ||
		strings.Contains(nameLower, "vbox") ||
		strings.Contains(nameLower, "hyper-v") {
		return false
	}

	return true
}

// getRealNetworkStats 获取真实的网络统计数据（优先使用gopsutil）
func (cm *ConnectionManager) getRealNetworkStats() (uint64, uint64, error) {
	log.Printf("🔧 getRealNetworkStats: 开始获取真实网络统计")

	// 🚀 方法1：优先使用gopsutil库（学习dashboard实现）
	if totalSent, totalRecv, err := cm.getStatsFromPsutil(); err == nil {
		log.Printf("✅ getRealNetworkStats: 通过gopsutil获取成功")
		return totalSent, totalRecv, nil
	} else {
		log.Printf("⚠️ getRealNetworkStats: gopsutil方法失败: %v", err)
	}

	// 🚀 方法2：回退到wmic命令
	if totalSent, totalRecv, err := cm.getStatsFromWmicCommand(); err == nil {
		log.Printf("✅ getRealNetworkStats: 通过wmic获取成功")
		return totalSent, totalRecv, nil
	} else {
		log.Printf("⚠️ getRealNetworkStats: wmic方法失败: %v", err)
	}

	// 如果所有方法都失败，返回真实的0值
	return 0, 0, fmt.Errorf("所有网络统计获取方法都失败")
}

// getStatsFromWmicCommand 使用wmic命令获取网络统计
func (cm *ConnectionManager) getStatsFromWmicCommand() (uint64, uint64, error) {
	log.Printf("🔧 getStatsFromWmicCommand: 使用wmic获取网络统计")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 使用wmic查询网络适配器统计
	cmd := exec.CommandContext(ctx, "wmic", "path", "Win32_PerfRawData_Tcpip_NetworkInterface",
		"get", "Name,BytesReceivedPerSec,BytesSentPerSec", "/format:csv")
	output, err := cmd.Output()
	if err != nil {
		return 0, 0, fmt.Errorf("wmic命令失败: %v", err)
	}

	var totalSent, totalRecv uint64
	lines := strings.Split(string(output), "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "Node") {
			continue
		}

		fields := strings.Split(line, ",")
		if len(fields) >= 4 {
			name := strings.TrimSpace(fields[3])
			if !cm.isValidNetworkInterface(name) {
				continue
			}

			// 解析字节统计
			if bytesRecv := strings.TrimSpace(fields[1]); bytesRecv != "" {
				if val, err := strconv.ParseUint(bytesRecv, 10, 64); err == nil {
					totalRecv += val
				}
			}
			if bytesSent := strings.TrimSpace(fields[2]); bytesSent != "" {
				if val, err := strconv.ParseUint(bytesSent, 10, 64); err == nil {
					totalSent += val
				}
			}

			log.Printf("📊 getStatsFromWmicCommand: 接口 %s - 接收=%s, 发送=%s",
				name, fields[1], fields[2])
		}
	}

	log.Printf("✅ getStatsFromWmicCommand: 总统计 - 发送=%d, 接收=%d", totalSent, totalRecv)
	return totalSent, totalRecv, nil
}

// calculateRealNetworkSpeed 计算真实的网络速度（学习dashboard实现）
func (cm *ConnectionManager) calculateRealNetworkSpeed(currentSent, currentRecv uint64) (float64, float64) {
	windowsNetworkMutex.Lock()
	defer windowsNetworkMutex.Unlock()

	now := time.Now()

	// 首次初始化
	if !windowsIsNetworkInitialized {
		windowsPrevBytesSent = currentSent
		windowsPrevBytesRecv = currentRecv
		windowsLastNetworkStat = now
		windowsIsNetworkInitialized = true
		log.Printf("🔧 Windows网络速度计算: 首次初始化，返回0速度（下次调用将显示真实速度）")
		return 0, 0
	}

	// 计算时间差（秒）
	duration := now.Sub(windowsLastNetworkStat).Seconds()
	if duration < 0.5 { // 减少最小时间间隔到0.5秒，学习dashboard
		log.Printf("🔧 Windows网络速度计算: 时间间隔太短(%.2fs)，返回上次速度", duration)
		return windowsLastUploadKbps, windowsLastDownloadKbps
	}

	// 计算字节差值
	sentDiff := currentSent - windowsPrevBytesSent
	recvDiff := currentRecv - windowsPrevBytesRecv

	// 检测计数器重置
	if currentSent < windowsPrevBytesSent || currentRecv < windowsPrevBytesRecv {
		log.Printf("⚠️ Windows网络速度计算: 检测到计数器重置，重新初始化")
		windowsPrevBytesSent = currentSent
		windowsPrevBytesRecv = currentRecv
		windowsLastNetworkStat = now
		return 0, 0
	}

	// 🚀 计算真实速率（KB/s）- 学习dashboard的计算方法
	uploadKbps := float64(sentDiff) / duration / 1024
	downloadKbps := float64(recvDiff) / duration / 1024

	log.Printf("📊 Windows网络速度计算: 时间间隔=%.2fs, 发送差值=%d, 接收差值=%d", duration, sentDiff, recvDiff)
	log.Printf("📊 Windows网络速度计算: 计算速度 - 上传=%.1fKB/s, 下载=%.1fKB/s", uploadKbps, downloadKbps)

	// 更新缓存变量
	windowsPrevBytesSent = currentSent
	windowsPrevBytesRecv = currentRecv
	windowsLastNetworkStat = now
	windowsLastUploadKbps = uploadKbps
	windowsLastDownloadKbps = downloadKbps

	return uploadKbps, downloadKbps
}

// getNetworkConnections 获取网络连接信息
func (cm *ConnectionManager) getNetworkConnections(protocol, state string) ([]NetworkConnection, error) {
	var connections []NetworkConnection

	log.Printf("🔍 开始获取网络连接信息: protocol=%s, state=%s", protocol, state)

	// 根据协议类型获取连接
	if protocol == "ALL" || protocol == "TCP" || protocol == "" {
		tcpConns, err := cm.getTCPConnections()
		if err == nil {
			connections = append(connections, tcpConns...)
			log.Printf("📊 获取到 %d 个TCP连接", len(tcpConns))
		} else {
			log.Printf("❌ 获取TCP连接失败: %v", err)
		}
	}

	if protocol == "ALL" || protocol == "UDP" {
		udpConns, err := cm.getUDPConnections()
		if err == nil {
			connections = append(connections, udpConns...)
			log.Printf("📊 获取到 %d 个UDP连接", len(udpConns))
		} else {
			log.Printf("❌ 获取UDP连接失败: %v", err)
		}
	}

	// 根据状态过滤
	if state != "" {
		var filtered []NetworkConnection
		for _, conn := range connections {
			if conn.State == state {
				filtered = append(filtered, conn)
			}
		}
		connections = filtered
		log.Printf("📊 状态过滤后剩余 %d 个连接", len(connections))
	}

	// 限制返回的连接数量，防止数据过大
	if len(connections) > MaxNetworkConnections {
		log.Printf("⚠️ 连接数量 %d 超过限制 %d，将截取前 %d 个连接",
			len(connections), MaxNetworkConnections, MaxNetworkConnections)
		connections = connections[:MaxNetworkConnections]
	}

	log.Printf("✅ 最终返回 %d 个网络连接", len(connections))
	return connections, nil
}

// getTCPConnections 获取TCP连接（包含PID信息）
func (cm *ConnectionManager) getTCPConnections() ([]NetworkConnection, error) {
	log.Printf("🔍 getTCPConnections: 开始执行netstat命令获取TCP连接")
	// 🚀 添加-o参数获取PID信息
	cmd := exec.Command("netstat", "-ano", "-p", "tcp")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("❌ getTCPConnections: 执行netstat命令失败: %v", err)
		return nil, fmt.Errorf("执行netstat命令失败: %v", err)
	}

	log.Printf("✅ getTCPConnections: netstat命令执行成功，输出长度: %d", len(output))
	log.Printf("🔍 getTCPConnections: netstat输出前500字符: %s", string(output[:min(500, len(output))]))

	connections := cm.parseNetstatOutputWithPID(string(output), "TCP")
	log.Printf("📊 getTCPConnections: 解析完成，获取到%d个TCP连接", len(connections))
	return connections, nil
}

// getUDPConnections 获取UDP连接（包含PID信息）
func (cm *ConnectionManager) getUDPConnections() ([]NetworkConnection, error) {
	log.Printf("🔍 getUDPConnections: 开始执行netstat命令获取UDP连接")
	// 🚀 添加-o参数获取PID信息
	cmd := exec.Command("netstat", "-ano", "-p", "udp")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("❌ getUDPConnections: 执行netstat命令失败: %v", err)
		return nil, fmt.Errorf("执行netstat命令失败: %v", err)
	}

	log.Printf("✅ getUDPConnections: netstat命令执行成功，输出长度: %d", len(output))
	log.Printf("🔍 getUDPConnections: netstat输出前500字符: %s", string(output[:min(500, len(output))]))

	connections := cm.parseNetstatOutputWithPID(string(output), "UDP")
	log.Printf("📊 getUDPConnections: 解析完成，获取到%d个UDP连接", len(connections))
	return connections, nil
}

// parseNetstatOutput 解析netstat输出
func (cm *ConnectionManager) parseNetstatOutput(output, protocol string) []NetworkConnection {
	var connections []NetworkConnection
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if !strings.HasPrefix(line, protocol) {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 4 {
			continue
		}

		conn := NetworkConnection{
			Protocol:        protocol,
			EstablishedTime: time.Now(),
		}

		// 解析本地地址
		if addr, port, err := cm.parseWindowsAddress(parts[1]); err == nil {
			conn.LocalAddress = addr
			conn.LocalPort = port
		}

		// 解析远程地址
		if len(parts) > 2 && parts[2] != "*:*" {
			if addr, port, err := cm.parseWindowsAddress(parts[2]); err == nil {
				conn.RemoteAddress = addr
				conn.RemotePort = port
			}
		}

		// 解析状态
		if len(parts) > 3 {
			conn.State = parts[3]
		} else if protocol == "UDP" {
			conn.State = "ESTABLISHED"
		}

		// 生成连接ID
		conn.ID = fmt.Sprintf("%s_%s:%d_%s:%d", protocol, conn.LocalAddress, conn.LocalPort, conn.RemoteAddress, conn.RemotePort)

		connections = append(connections, conn)
	}

	return connections
}

// parseWindowsAddress 解析Windows地址格式
func (cm *ConnectionManager) parseWindowsAddress(addrStr string) (string, int, error) {
	parts := strings.Split(addrStr, ":")
	if len(parts) != 2 {
		return "", 0, fmt.Errorf("无效的地址格式: %s", addrStr)
	}

	addr := parts[0]
	if addr == "0.0.0.0" {
		addr = "127.0.0.1"
	}

	port, err := strconv.Atoi(parts[1])
	if err != nil {
		return "", 0, fmt.Errorf("解析端口失败: %v", err)
	}

	return addr, port, nil
}

// closeNetworkConnection 关闭网络连接
func (cm *ConnectionManager) closeNetworkConnection(connectionID, protocol, localAddr string, localPort int, remoteAddr string, remotePort int) error {
	log.Printf("尝试关闭连接: %s %s:%d -> %s:%d", protocol, localAddr, localPort, remoteAddr, remotePort)

	// 检查是否有管理员权限
	if !cm.isAdmin() {
		return fmt.Errorf("Windows连接关闭功能需要管理员权限，当前不支持")
	}

	// 第一层：尝试使用Windows API关闭连接
	if protocol == "TCP" {
		err := cm.closeTCPConnectionWithAPI(localAddr, localPort, remoteAddr, remotePort)
		if err == nil {
			log.Printf("✅ 使用Windows API成功关闭TCP连接")
			return nil
		}
		log.Printf("⚠️ Windows API关闭连接失败: %v，尝试netsh命令", err)
	}

	// 第二层：使用netsh命令关闭连接
	err := cm.closeConnectionWithNetsh(protocol, localAddr, localPort, remoteAddr, remotePort)
	if err == nil {
		log.Printf("✅ 使用netsh命令成功关闭连接")
		return nil
	}

	log.Printf("❌ netsh命令也失败: %v", err)
	return fmt.Errorf("关闭连接失败，已尝试Windows API和netsh命令: %v", err)
}

// isAdmin 检查当前进程是否具有管理员权限（使用Windows API）
func (cm *ConnectionManager) isAdmin() bool {
	// 🚀 使用Windows shell32.dll的IsUserAnAdmin API
	mod := syscall.NewLazyDLL("shell32.dll")
	proc := mod.NewProc("IsUserAnAdmin")
	r1, _, _ := proc.Call()

	isAdminResult := r1 != 0
	if isAdminResult {
		log.Printf("✅ 检测到管理员权限")
	} else {
		log.Printf("⚠️ 未检测到管理员权限")
	}

	return isAdminResult
}

// closeTCPConnectionWithAPI 使用Windows API关闭TCP连接
func (cm *ConnectionManager) closeTCPConnectionWithAPI(localAddr string, localPort int, remoteAddr string, remotePort int) error {
	// 定义MIB_TCPROW结构体
	type MIB_TCPROW struct {
		State      uint32
		LocalAddr  uint32
		LocalPort  uint32
		RemoteAddr uint32
		RemotePort uint32
	}

	// 转换IP地址为uint32
	localIP := cm.ipToUint32(localAddr)
	remoteIP := cm.ipToUint32(remoteAddr)

	if localIP == 0 || remoteIP == 0 {
		return fmt.Errorf("无效的IP地址")
	}

	// 创建TCP行结构
	tcpRow := MIB_TCPROW{
		State:      12, // MIB_TCP_STATE_DELETE_TCB
		LocalAddr:  localIP,
		LocalPort:  cm.htons(uint16(localPort)),
		RemoteAddr: remoteIP,
		RemotePort: cm.htons(uint16(remotePort)),
	}

	// 调用SetTcpEntry API
	ret, _, err := procSetTcpEntry.Call(uintptr(unsafe.Pointer(&tcpRow)))
	if ret != 0 {
		// 🚀 改进错误处理：检查特殊情况
		if err != nil {
			errMsg := err.Error()
			// 情况1：操作实际成功
			if errMsg == "The operation completed successfully." {
				log.Printf("✅ SetTcpEntry成功（Windows API返回码%d，操作实际成功）", ret)
				return nil
			}
			// 情况2：错误代码317 - 无法找到错误消息文本（但操作可能成功）
			if ret == 317 || errMsg == "The system cannot find message text for message number 0x%1 in the message file for System." {
				log.Printf("✅ SetTcpEntry可能成功（错误代码317表示无法格式化错误消息，但TCP操作可能已完成）")
				return nil
			}
		}
		return fmt.Errorf("SetTcpEntry失败: %v (错误代码: %d)", err, ret)
	}

	return nil
}

// closeConnectionWithNetsh 使用netsh命令关闭连接
func (cm *ConnectionManager) closeConnectionWithNetsh(protocol, localAddr string, localPort int, remoteAddr string, remotePort int) error {
	if protocol == "TCP" {
		// 使用netsh命令关闭TCP连接
		cmd := exec.Command("netsh", "int", "ipv4", "delete", "tcpconnection",
			fmt.Sprintf("%s:%d", localAddr, localPort),
			fmt.Sprintf("%s:%d", remoteAddr, remotePort))

		output, err := cmd.CombinedOutput()
		if err != nil {
			return fmt.Errorf("netsh命令执行失败: %v, 输出: %s", err, string(output))
		}

		return nil
	}

	return fmt.Errorf("netsh不支持关闭%s连接", protocol)
}

// ipToUint32 将IP地址字符串转换为uint32
func (cm *ConnectionManager) ipToUint32(ip string) uint32 {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return 0
	}

	var result uint32
	for i, part := range parts {
		val, err := strconv.Atoi(part)
		if err != nil || val < 0 || val > 255 {
			return 0
		}
		result |= uint32(val) << (8 * (3 - i))
	}

	return result
}

// htons 主机字节序转网络字节序
func (cm *ConnectionManager) htons(port uint16) uint32 {
	return uint32(((port & 0xFF) << 8) | ((port >> 8) & 0xFF))
}

// calculateInterfaceSpeedWindows 计算Windows网络接口的实时速度
func (cm *ConnectionManager) calculateInterfaceSpeedWindows(iface *NetInterface) {
	now := time.Now()

	// 获取或创建缓存
	cm.interfaceCacheMutex.Lock()
	cache, exists := cm.interfaceSpeedCache[iface.Name]
	if !exists {
		// 首次采样，创建缓存但不计算速度
		cache = &InterfaceSpeedCache{
			LastBytesSent:     iface.BytesSent,
			LastBytesReceived: iface.BytesReceived,
			LastTimestamp:     now,
			CurrentUpSpeed:    0,
			CurrentDownSpeed:  0,
		}
		cm.interfaceSpeedCache[iface.Name] = cache
		cm.interfaceCacheMutex.Unlock()

		// 首次采样，速度设为0
		iface.Speed = 0
		return
	}
	cm.interfaceCacheMutex.Unlock()

	// 计算时间差（秒）
	timeDiff := now.Sub(cache.LastTimestamp).Seconds()
	if timeDiff < 0.1 { // 避免时间间隔太短导致的计算误差
		// 使用缓存的速度值
		iface.Speed = uint64(cache.CurrentUpSpeed + cache.CurrentDownSpeed)
		return
	}

	// 计算字节差
	bytesSentDiff := int64(iface.BytesSent) - int64(cache.LastBytesSent)
	bytesRecvDiff := int64(iface.BytesReceived) - int64(cache.LastBytesReceived)

	// 处理计数器重置的情况（通常不会发生，但为了健壮性）
	if bytesSentDiff < 0 {
		bytesSentDiff = 0
	}
	if bytesRecvDiff < 0 {
		bytesRecvDiff = 0
	}

	// 计算速度 (字节/秒)
	upSpeed := float64(bytesSentDiff) / timeDiff
	downSpeed := float64(bytesRecvDiff) / timeDiff

	// 更新缓存
	cm.interfaceCacheMutex.Lock()
	cache.LastBytesSent = iface.BytesSent
	cache.LastBytesReceived = iface.BytesReceived
	cache.LastTimestamp = now
	cache.CurrentUpSpeed = upSpeed
	cache.CurrentDownSpeed = downSpeed
	cm.interfaceCacheMutex.Unlock()

	// 设置接口速度（总速度 = 上传 + 下载）
	totalSpeed := upSpeed + downSpeed
	if totalSpeed < 0 {
		totalSpeed = 0
	}
	iface.Speed = uint64(totalSpeed)

	log.Printf("🚀 Windows接口 %s 速度计算: 上传=%.2f B/s, 下载=%.2f B/s, 总计=%.2f B/s",
		iface.Name, upSpeed, downSpeed, totalSpeed)
}

// getStatsFromPsutilWindows 使用gopsutil获取Windows接口统计数据
func (cm *ConnectionManager) getStatsFromPsutilWindows(iface *NetInterface) bool {
	log.Printf("🔧 getStatsFromPsutilWindows: 尝试通过gopsutil获取接口%s统计", iface.Name)

	// 🚀 直接使用 gopsutil 获取网络接口统计信息
	ioCounters, err := psutilNet.IOCounters(true) // true表示获取每个接口的统计
	if err != nil {
		log.Printf("❌ getStatsFromPsutilWindows: gopsutil获取接口统计失败: %v", err)
		return false
	}

	log.Printf("🔍 getStatsFromPsutilWindows: gopsutil返回了%d个接口统计", len(ioCounters))

	// 打印所有可用的接口名称用于调试
	for i, counter := range ioCounters {
		log.Printf("🔍 getStatsFromPsutilWindows: 接口[%d] 名称='%s', 发送=%d, 接收=%d",
			i, counter.Name, counter.BytesSent, counter.BytesRecv)
	}

	// 查找对应的接口
	for _, counter := range ioCounters {
		if counter.Name == iface.Name {
			// 直接从 gopsutil 获取真实的流量统计数据
			oldSent := iface.BytesSent
			oldRecv := iface.BytesReceived

			iface.BytesSent = counter.BytesSent
			iface.BytesReceived = counter.BytesRecv
			iface.PacketsSent = counter.PacketsSent
			iface.PacketsRecv = counter.PacketsRecv
			iface.ErrorsIn = counter.Errin
			iface.ErrorsOut = counter.Errout

			log.Printf("✅ getStatsFromPsutilWindows: 接口%s统计更新 - 发送:%d->%d字节, 接收:%d->%d字节",
				iface.Name, oldSent, iface.BytesSent, oldRecv, iface.BytesReceived)
			return true
		}
	}

	log.Printf("⚠️ getStatsFromPsutilWindows: 未找到接口%s的统计信息，可能接口名不匹配", iface.Name)
	return false
}

// parseNetstatOutputWithPID 解析包含PID的netstat输出
func (cm *ConnectionManager) parseNetstatOutputWithPID(output, protocol string) []NetworkConnection {
	var connections []NetworkConnection
	lines := strings.Split(output, "\n")

	log.Printf("🔍 parseNetstatOutputWithPID: 开始解析%s协议，总行数: %d", protocol, len(lines))

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if !strings.HasPrefix(line, protocol) {
			if i < 10 { // 只打印前10行的跳过信息，避免日志过多
				log.Printf("🔍 parseNetstatOutputWithPID: 跳过非%s行: %s", protocol, line)
			}
			continue
		}

		log.Printf("✅ parseNetstatOutputWithPID: 匹配%s行: %s", protocol, line)

		parts := strings.Fields(line)
		if len(parts) < 5 { // 至少需要协议、本地地址、远程地址、状态、PID
			log.Printf("⚠️ parseNetstatOutputWithPID: 字段数不足(%d<5)，跳过行: %s", len(parts), line)
			continue
		}

		log.Printf("🔍 parseNetstatOutputWithPID: 字段解析 - 字段数:%d, 字段:%v", len(parts), parts)

		conn := NetworkConnection{
			Protocol:        protocol,
			EstablishedTime: time.Now(),
		}

		// 解析本地地址
		if addr, port, err := cm.parseWindowsAddress(parts[1]); err == nil {
			conn.LocalAddress = addr
			conn.LocalPort = port
		}

		// 解析远程地址
		if len(parts) > 2 && parts[2] != "*:*" {
			if addr, port, err := cm.parseWindowsAddress(parts[2]); err == nil {
				conn.RemoteAddress = addr
				conn.RemotePort = port
			}
		}

		// 解析状态
		if len(parts) > 3 {
			conn.State = parts[3]
		} else if protocol == "UDP" {
			conn.State = "ESTABLISHED"
		}

		// 🚀 解析PID（最后一个字段）
		if len(parts) >= 5 {
			pidStr := parts[len(parts)-1]
			log.Printf("🔍 parseNetstatOutputWithPID: 尝试解析PID字符串='%s'", pidStr)
			if pid, err := strconv.Atoi(pidStr); err == nil {
				conn.PID = pid
				log.Printf("✅ parseNetstatOutputWithPID: 成功解析PID=%d", pid)
				// 🚀 利用现有进程缓存获取进程名
				processName := cm.getProcessNameFromCacheWindows(pid)
				conn.ProcessName = processName
				log.Printf("🔍 parseNetstatOutputWithPID: PID=%d 对应进程名='%s'", pid, processName)
			} else {
				log.Printf("❌ parseNetstatOutputWithPID: 解析PID失败: %v", err)
			}
		} else {
			log.Printf("⚠️ parseNetstatOutputWithPID: 字段数不足，无法解析PID，字段数=%d", len(parts))
		}

		// 生成连接ID
		conn.ID = fmt.Sprintf("%s_%s:%d_%s:%d", protocol, conn.LocalAddress, conn.LocalPort, conn.RemoteAddress, conn.RemotePort)

		connections = append(connections, conn)
	}

	return connections
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// getProcessNameFromCacheWindows 从Windows进程缓存中获取进程名
func (cm *ConnectionManager) getProcessNameFromCacheWindows(pid int) string {
	// 🚀 利用现有的进程缓存机制
	processCacheMutex.RLock()
	if cachedInfo, exists := processInfoCache[int32(pid)]; exists {
		processCacheMutex.RUnlock()
		return cachedInfo.Name
	}
	processCacheMutex.RUnlock()

	// 缓存未命中，尝试直接获取进程信息
	if p, err := process.NewProcess(int32(pid)); err == nil {
		if name, err := p.Name(); err == nil && name != "" {
			// 🚀 将进程信息存储到缓存中
			processCacheMutex.Lock()
			processInfoCache[int32(pid)] = &ProcessFullInfo{
				PID:  int32(pid),
				Name: name,
			}
			processCacheMutex.Unlock()
			log.Printf("✅ Windows缓存进程名: PID=%d, Name=%s", pid, name)
			return name
		}
	}

	// 如果获取失败，返回空字符串而不是格式化的PID
	log.Printf("⚠️ Windows无法获取进程名: PID=%d", pid)
	return ""
}
