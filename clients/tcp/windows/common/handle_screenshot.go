//go:build windows
// +build windows

package common

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"log"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

// ScreenshotRequest 截图请求
type ScreenshotRequest struct {
	TaskID       uint64 `json:"task_id"`       // 任务ID
	Type         int    `json:"type"`          // 截图类型: 0=全屏, 1=活动窗口, 2=指定区域
	MonitorID    int    `json:"monitor_id"`    // 显示器ID (多显示器支持)
	MonitorIndex int    `json:"monitor_index"` // 显示器索引 (前端兼容)
	X            int    `json:"x"`             // 区域截图的X坐标
	Y            int    `json:"y"`             // 区域截图的Y坐标
	Width        int    `json:"width"`         // 区域截图的宽度
	Height       int    `json:"height"`        // 区域截图的高度
	Format       string `json:"format"`        // 图片格式: "png", "jpeg", "bmp"
	Quality      int    `json:"quality"`       // JPEG质量 (1-100)
}

// ScreenshotResponse 截图响应
type ScreenshotResponse struct {
	TaskID    uint64 `json:"task_id"`            // 任务ID
	Success   bool   `json:"success"`            // 是否成功
	Error     string `json:"error"`              // 错误信息
	ImageData []byte `json:"image_data"`         // 图片数据
	Width     int    `json:"width"`              // 图片宽度
	Height    int    `json:"height"`             // 图片高度
	Format    string `json:"format"`             // 图片格式
	Size      int64  `json:"size"`               // 文件大小
	Timestamp int64  `json:"timestamp"`          // 截图时间戳
	FrameID   uint64 `json:"frame_id,omitempty"` // 帧ID (仅用于流模式)
}

// ScreenshotListResponse 截图列表响应
type ScreenshotListResponse struct {
	TaskID      uint64           `json:"task_id"`     // 任务ID
	Success     bool             `json:"success"`     // 操作是否成功
	Screenshots []ScreenshotInfo `json:"screenshots"` // 截图列表
	Error       string           `json:"error"`       // 错误信息
	Count       int              `json:"count"`       // 截图总数
}

// ScreenshotInfo 截图信息
type ScreenshotInfo struct {
	ID        uint   `json:"id"`         // 截图ID
	ClientID  uint   `json:"client_id"`  // 客户端ID
	Filename  string `json:"filename"`   // 文件名
	FilePath  string `json:"file_path"`  // 文件路径
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 文件大小
	Timestamp int64  `json:"timestamp"`  // 截图时间戳
	CreatedAt string `json:"created_at"` // 创建时间
}

// ScreenshotDeleteResponse 删除截图响应
type ScreenshotDeleteResponse struct {
	TaskID   uint64 `json:"task_id"`  // 任务ID
	Success  bool   `json:"success"`  // 操作是否成功
	Filename string `json:"filename"` // 删除的文件名
	Error    string `json:"error"`    // 错误信息
}

// ScreenStreamStartResponse 屏幕流开始响应
type ScreenStreamStartResponse struct {
	TaskID   uint64 `json:"task_id"`   // 任务ID
	Success  bool   `json:"success"`   // 操作是否成功
	Error    string `json:"error"`     // 错误信息
	StreamID string `json:"stream_id"` // 流ID
}

// ScreenStreamStopResponse 屏幕流停止响应
type ScreenStreamStopResponse struct {
	TaskID  uint64 `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	Error   string `json:"error"`   // 错误信息
}

// 扩展的ScreenshotRequest结构体，添加流相关字段
type ExtendedScreenshotRequest struct {
	ScreenshotRequest
	FPS                      int  `json:"fps,omitempty"`                        // 帧率
	EnableDiffDetection      bool `json:"enable_diff_detection,omitempty"`      // 启用差异检测
	DiffThreshold            int  `json:"diff_threshold,omitempty"`             // 差异阈值百分比
	CPULimit                 int  `json:"cpu_limit,omitempty"`                  // CPU使用限制百分比
	BandwidthLimit           int  `json:"bandwidth_limit,omitempty"`            // 带宽限制 KB/s
	EnableMemoryOptimization bool `json:"enable_memory_optimization,omitempty"` // 启用内存优化
}

// ScreenStreamDataResponse 屏幕流数据响应
type ScreenStreamDataResponse struct {
	TaskID    uint64 `json:"task_id"`    // 任务ID
	Success   bool   `json:"success"`    // 操作是否成功
	StreamID  string `json:"stream_id"`  // 流ID
	FrameData []byte `json:"frame_data"` // 帧数据
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 帧大小
	Timestamp int64  `json:"timestamp"`  // 时间戳
	Error     string `json:"error"`      // 错误信息
}

// 显示器信息结构体
type DisplayInfo struct {
	ID        int    `json:"id"`         // 显示器ID
	Name      string `json:"name"`       // 显示器名称
	X         int    `json:"x"`          // X坐标
	Y         int    `json:"y"`          // Y坐标
	Width     int    `json:"width"`      // 宽度
	Height    int    `json:"height"`     // 高度
	IsPrimary bool   `json:"is_primary"` // 是否主显示器
}

// Windows API 常量
const (
	// 系统度量常量
	SM_CXSCREEN        = 0  // 主显示器宽度
	SM_CYSCREEN        = 1  // 主显示器高度
	SM_XVIRTUALSCREEN  = 76 // 虚拟屏幕左上角X坐标
	SM_YVIRTUALSCREEN  = 77 // 虚拟屏幕左上角Y坐标
	SM_CXVIRTUALSCREEN = 78 // 虚拟屏幕宽度
	SM_CYVIRTUALSCREEN = 79 // 虚拟屏幕高度

	// 显示器信息常量
	MONITORINFOF_PRIMARY     = 0x00000001
	MONITOR_DEFAULTTOPRIMARY = 0x00000001

	// 位图操作常量
	SRCCOPY        = 0x00CC0020
	BI_RGB         = 0
	DIB_RGB_COLORS = 0

	// 窗口相关常量
	PW_CLIENTONLY        = 0x00000001
	PW_RENDERFULLCONTENT = 0x00000002

	// 🚀 DPI感知常量
	DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2 = ^uintptr(4 - 1)
	DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE    = ^uintptr(3 - 1)
	DPI_AWARENESS_CONTEXT_SYSTEM_AWARE         = ^uintptr(2 - 1)
	DPI_AWARENESS_CONTEXT_UNAWARE              = ^uintptr(1 - 1)
)

// Windows API 结构体
type RECT struct {
	Left   int32
	Top    int32
	Right  int32
	Bottom int32
}

type POINT struct {
	X, Y int32
}

type MONITORINFO struct {
	CbSize    uint32
	RcMonitor RECT
	RcWork    RECT
	DwFlags   uint32
}

type BITMAPINFOHEADER struct {
	BiSize          uint32
	BiWidth         int32
	BiHeight        int32
	BiPlanes        uint16
	BiBitCount      uint16
	BiCompression   uint32
	BiSizeImage     uint32
	BiXPelsPerMeter int32
	BiYPelsPerMeter int32
	BiClrUsed       uint32
	BiClrImportant  uint32
}

type BITMAPINFO struct {
	BmiHeader BITMAPINFOHEADER
	BmiColors [1]uint32
}

// Windows API 函数声明
var (
	user32   = windows.NewLazySystemDLL("user32.dll")
	gdi32    = windows.NewLazySystemDLL("gdi32.dll")
	kernel32 = windows.NewLazySystemDLL("kernel32.dll")

	// User32 API
	procGetDC                = user32.NewProc("GetDC")
	procReleaseDC            = user32.NewProc("ReleaseDC")
	procGetWindowDC          = user32.NewProc("GetWindowDC")
	procGetSystemMetrics     = user32.NewProc("GetSystemMetrics")
	procEnumDisplayMonitors  = user32.NewProc("EnumDisplayMonitors")
	procGetMonitorInfoW      = user32.NewProc("GetMonitorInfoW")
	procGetForegroundWindow  = user32.NewProc("GetForegroundWindow")
	procGetWindowRect        = user32.NewProc("GetWindowRect")
	procGetClientRect        = user32.NewProc("GetClientRect")
	procClientToScreen       = user32.NewProc("ClientToScreen")
	procPrintWindow          = user32.NewProc("PrintWindow")
	procIsWindow             = user32.NewProc("IsWindow")
	procIsWindowVisible      = user32.NewProc("IsWindowVisible")
	procGetWindowTextW       = user32.NewProc("GetWindowTextW")
	procGetWindowTextLengthW = user32.NewProc("GetWindowTextLengthW")

	// 🚀 DPI感知API
	procSetProcessDpiAwarenessContext = user32.NewProc("SetProcessDpiAwarenessContext")
	procGetSystemMetricsForDpi        = user32.NewProc("GetSystemMetricsForDpi")
	procGetDpiForWindow               = user32.NewProc("GetDpiForWindow")
	procGetDpiForSystem               = user32.NewProc("GetDpiForSystem")

	// GDI32 API
	procCreateCompatibleDC     = gdi32.NewProc("CreateCompatibleDC")
	procDeleteDC               = gdi32.NewProc("DeleteDC")
	procCreateCompatibleBitmap = gdi32.NewProc("CreateCompatibleBitmap")
	procSelectObject           = gdi32.NewProc("SelectObject")
	procDeleteObject           = gdi32.NewProc("DeleteObject")
	procBitBlt                 = gdi32.NewProc("BitBlt")
	procStretchBlt             = gdi32.NewProc("StretchBlt")
	procGetDIBits              = gdi32.NewProc("GetDIBits")
	procSetDIBits              = gdi32.NewProc("SetDIBits")
	procCreateDIBSection       = gdi32.NewProc("CreateDIBSection")
	procGetDeviceCaps          = gdi32.NewProc("GetDeviceCaps")

	// Kernel32 API
	procGetLastError = kernel32.NewProc("GetLastError")
	procGlobalAlloc  = kernel32.NewProc("GlobalAlloc")
	procGlobalFree   = kernel32.NewProc("GlobalFree")
	procGlobalLock   = kernel32.NewProc("GlobalLock")
	procGlobalUnlock = kernel32.NewProc("GlobalUnlock")
)

// 屏幕流状态管理
type ScreenStreamState struct {
	isRunning                bool
	stopChannel              chan bool
	ctx                      context.Context    // 屏幕流专用context
	cancel                   context.CancelFunc // 屏幕流专用cancel函数
	frameRate                int
	quality                  int
	format                   string
	lastFrame                []byte
	lastHash                 string
	screenType               int  // 截图类型：0=全屏，1=活动窗口，2=区域
	enableDiffDetection      bool // 启用差异检测
	diffThreshold            int  // 差异阈值百分比
	cpuLimit                 int  // CPU使用限制百分比
	bandwidthLimit           int  // 带宽限制 KB/s
	enableMemoryOptimization bool // 启用内存优化
}

var streamState = &ScreenStreamState{
	isRunning:                false,
	stopChannel:              make(chan bool, 1),
	frameRate:                10,    // 默认10fps
	screenType:               0,     // 默认全屏
	enableDiffDetection:      false, // 默认关闭差异检测
	diffThreshold:            5,     // 默认5%差异阈值
	cpuLimit:                 0,     // 默认无CPU限制
	bandwidthLimit:           0,     // 默认无带宽限制
	enableMemoryOptimization: true,  // 默认启用内存优化
}

// 🚀 DPI感知初始化
func init() {
	// 设置进程为Per-Monitor DPI感知模式
	setProcessDpiAwarenessContext(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2)
	log.Printf("🖥️ 已启用Per-Monitor DPI感知模式")
}

// 处理截图请求的主函数
func (cm *ConnectionManager) handleScreenshotRequest(packet *Packet) {
	switch packet.Header.Code {
	case Pic:
		cm.handleScreenshot(packet)
	case StreamStart:
		cm.handleScreenStreamStart(packet)
	case StreamStop:
		cm.handleScreenStreamStop(packet)
	case StreamData:
		cm.handleScreenStream(packet)
	case MonitorList:
		cm.handleMonitorList(packet)
	default:
		log.Printf("未知的截图操作代码: %d", packet.Header.Code)
	}
}

// 处理单次截图请求
func (cm *ConnectionManager) handleScreenshot(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ScreenshotResponse{
		TaskID:  0,
		Success: false,
		Error:   "解析请求失败",
	}

	var req ScreenshotRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析截图请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "解析请求失败: " + err.Error()
		cm.sendResp(Screenshot, Pic, errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	log.Printf("🖼️ 开始处理截图请求 - 类型: %d, 格式: %s, 质量: %d, 显示器索引: %d", req.Type, req.Format, req.Quality, req.MonitorIndex)

	// 如果指定了显示器索引且类型为全屏截图，则改为指定显示器截图
	if req.MonitorIndex > 0 && req.Type == 0 {
		req.Type = 2 // 改为指定显示器截图
		req.MonitorID = req.MonitorIndex
	}

	// 执行截图
	imageData, width, height, err := cm.captureScreen(&req)
	if err != nil {
		log.Printf("❌ 截图失败: %v", err)
		errorResp.Error = "截图失败: " + err.Error()
		cm.sendResp(Screenshot, Pic, errorResp)
		return
	}

	// 发送成功响应
	cm.sendResp(Screenshot, Pic, ScreenshotResponse{
		TaskID:    req.TaskID,
		Success:   true,
		ImageData: imageData,
		Width:     width,
		Height:    height,
		Format:    req.Format,
		Size:      int64(len(imageData)),
		Timestamp: time.Now().Unix(),
	})

	log.Printf("✅ 截图完成 - 尺寸: %dx%d, 大小: %d bytes", width, height, len(imageData))
}

// 处理屏幕流启动请求
func (cm *ConnectionManager) handleScreenStreamStart(packet *Packet) {
	var req ExtendedScreenshotRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流启动请求失败: %v", err)
		return
	}

	// 如果已经在运行，先停止
	if streamState.isRunning {
		if streamState.cancel != nil {
			streamState.cancel()
		}
		streamState.stopChannel <- true
		time.Sleep(100 * time.Millisecond)
	}

	// 创建屏幕流专用的context，继承自连接的context
	streamState.ctx, streamState.cancel = context.WithCancel(cm.ctx)

	// 设置基本流参数
	if req.Quality > 0 && req.Quality <= 100 {
		streamState.quality = req.Quality
	} else {
		streamState.quality = 85 // 流模式提高默认质量
	}

	if req.Format != "" {
		streamState.format = req.Format
	} else {
		streamState.format = "jpeg"
	}

	// 设置截图类型
	streamState.screenType = req.Type

	// 设置高级参数 (从请求中解析，如果有的话)
	// 🚀 支持最高240FPS
	if req.FPS > 0 && req.FPS <= 240 {
		streamState.frameRate = req.FPS
		log.Printf("🎯 设置帧率: %d FPS", req.FPS)

		// 🚀 智能优化1: 高帧率强制使用JPEG格式
		if req.FPS > 100 && req.Format != "jpeg" {
			req.Format = "jpeg"
			streamState.format = "jpeg"
			log.Printf("⚡ 高帧率优化: 帧率 %d FPS > 100，强制使用JPEG格式", req.FPS)
		}
	}

	// 差异检测设置
	streamState.enableDiffDetection = req.EnableDiffDetection
	if req.DiffThreshold > 0 && req.DiffThreshold <= 50 {
		streamState.diffThreshold = req.DiffThreshold
	}

	// 性能控制设置
	if req.CPULimit > 0 && req.CPULimit <= 100 {
		streamState.cpuLimit = req.CPULimit
	}
	if req.BandwidthLimit >= 0 {
		streamState.bandwidthLimit = req.BandwidthLimit
	}
	streamState.enableMemoryOptimization = req.EnableMemoryOptimization

	// 启动屏幕流
	streamState.isRunning = true
	streamState.stopChannel = make(chan bool, 1)

	go cm.runScreenStream(req.TaskID)

	log.Printf("🎥 屏幕流已启动 - 帧率: %d fps, 质量: %d%%, 格式: %s, 差异检测: %v, CPU限制: %d%%, 带宽限制: %d KB/s",
		streamState.frameRate, streamState.quality, streamState.format,
		streamState.enableDiffDetection, streamState.cpuLimit, streamState.bandwidthLimit)

	// 发送启动成功响应
	cm.sendResp(Screenshot, StreamStart, ScreenStreamStartResponse{
		TaskID:   req.TaskID,
		Success:  true,
		Error:    "",
		StreamID: fmt.Sprintf("stream_%d_%d", req.TaskID, time.Now().Unix()),
	})
}

// 处理屏幕流停止请求
func (cm *ConnectionManager) handleScreenStreamStop(packet *Packet) {
	var req ScreenshotRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流停止请求失败: %v", err)
		return
	}

	// 停止屏幕流
	if streamState.isRunning {
		streamState.isRunning = false
		if streamState.cancel != nil {
			streamState.cancel()
		}
		select {
		case streamState.stopChannel <- true:
		default:
		}
		log.Printf("🛑 屏幕流已停止")

		// 发送停止成功响应
		cm.sendResp(Screenshot, StreamStop, ScreenStreamStopResponse{
			TaskID:  req.TaskID,
			Success: true,
			Error:   "",
		})
	} else {
		log.Printf("⚠️ 屏幕流未运行")

		// 发送停止失败响应
		cm.sendResp(Screenshot, StreamStop, ScreenStreamStopResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "屏幕流未运行",
		})
	}
}

// 运行屏幕流
func (cm *ConnectionManager) runScreenStream(taskID uint64) {
	var frameCount uint64
	startTime := time.Now()

	// 🕒 性能计时统计
	var totalCaptureTime time.Duration
	var totalDiffTime time.Duration
	var totalEncodeTime time.Duration
	var totalSendTime time.Duration

	defer func() {
		streamState.isRunning = false
		if streamState.cancel != nil {
			streamState.cancel()
		}
		// 记录屏幕流线程退出日志和统计信息
		duration := time.Since(startTime)
		avgFPS := float64(frameCount) / duration.Seconds()
		log.Printf("🛑 屏幕流线程退出 - 总帧数: %d, 运行时长: %.2fs, 平均帧率: %.2f fps",
			frameCount, duration.Seconds(), avgFPS)

		// 🕒 详细性能统计
		if frameCount > 0 {
			log.Printf("📊 性能统计详情:")
			log.Printf("   截图总耗时: %v (平均: %v/帧)", totalCaptureTime, totalCaptureTime/time.Duration(frameCount))
			log.Printf("   差异检测总耗时: %v (平均: %v/帧)", totalDiffTime, totalDiffTime/time.Duration(frameCount))
			log.Printf("   编码总耗时: %v (平均: %v/帧)", totalEncodeTime, totalEncodeTime/time.Duration(frameCount))
			log.Printf("   发送总耗时: %v (平均: %v/帧)", totalSendTime, totalSendTime/time.Duration(frameCount))

			// 计算各步骤占比
			totalProcessTime := totalCaptureTime + totalDiffTime + totalEncodeTime + totalSendTime
			if totalProcessTime > 0 {
				log.Printf("📈 各步骤耗时占比:")
				log.Printf("   截图: %.1f%%", float64(totalCaptureTime)/float64(totalProcessTime)*100)
				log.Printf("   差异检测: %.1f%%", float64(totalDiffTime)/float64(totalProcessTime)*100)
				log.Printf("   编码: %.1f%%", float64(totalEncodeTime)/float64(totalProcessTime)*100)
				log.Printf("   发送: %.1f%%", float64(totalSendTime)/float64(totalProcessTime)*100)
			}
		}
	}()

	frameDuration := time.Duration(1000/streamState.frameRate) * time.Millisecond
	ticker := time.NewTicker(frameDuration)
	defer ticker.Stop()

	var lastImageData []byte

	log.Printf("🎥 屏幕流开始运行 - 帧率: %d fps, 质量: %d%%, 格式: %s", streamState.frameRate, streamState.quality, streamState.format)

	for {
		select {
		case <-streamState.stopChannel:
			return
		case <-streamState.ctx.Done():
			log.Printf("� 屏幕流context已取消，屏幕流线程退出")
			return
		case <-ticker.C:
			frameStart := time.Now()
			log.Printf("🎬 开始处理帧 #%d", frameCount+1)

			// 声明本帧的计时变量
			var captureTime, diffTime, bandwidthTime, cacheTime, sendTime, memTime time.Duration

			// CPU限制：通过休眠时间来模拟限制
			if streamState.cpuLimit > 0 && streamState.cpuLimit < 100 {
				cpuSleepTime := time.Duration((100-streamState.cpuLimit)*10) * time.Microsecond
				time.Sleep(cpuSleepTime)
				log.Printf("🔧 CPU限制休眠: %v", cpuSleepTime)
			}

			// 创建截图请求
			req := &ScreenshotRequest{
				Type:    streamState.screenType,
				Format:  streamState.format,
				Quality: streamState.quality,
				TaskID:  taskID,
			}

			// 🕒 步骤1: 执行截图
			captureStart := time.Now()
			imageData, width, height, err := cm.captureScreen(req)
			captureTime = time.Since(captureStart)
			totalCaptureTime += captureTime

			if err != nil {
				log.Printf("❌ 屏幕流截图失败 (耗时: %v): %v", captureTime, err)
				continue
			}
			log.Printf("📷 截图完成 (%dx%d, %.1fKB, 耗时: %v)", width, height, float64(len(imageData))/1024, captureTime)

			// 🕒 步骤2: 差异检测
			diffStart := time.Now()
			var diffPercent float64
			shouldSend := true

			if streamState.enableDiffDetection && lastImageData != nil {
				diffPercent = cm.calculateImageDifference(lastImageData, imageData)
				shouldSend = diffPercent >= float64(streamState.diffThreshold)
			} else {
				diffPercent = 100.0 // 第一帧或未启用差异检测
			}

			diffTime = time.Since(diffStart)
			totalDiffTime += diffTime
			log.Printf("🔍 差异检测完成 (差异: %.1f%%, 阈值: %.1f%%, 需发送: %v, 耗时: %v)",
				diffPercent, float64(streamState.diffThreshold), shouldSend, diffTime)

			if !shouldSend {
				log.Printf("⏭️  帧 #%d 跳过发送 - 差异过小", frameCount+1)
				continue // 跳过相似帧
			}

			// 🕒 步骤3: 带宽限制检查和质量调整
			bandwidthStart := time.Now()
			if streamState.bandwidthLimit > 0 {
				maxSize := streamState.bandwidthLimit * 1024 // KB转字节
				if len(imageData) > maxSize {
					log.Printf("📊 数据包过大 (%.1fKB > %.1fKB)，优化压缩策略",
						float64(len(imageData))/1024, float64(maxSize)/1024)

					// 🚀 智能压缩策略：优先改用JPEG格式
					if req.Format == "png" {
						log.Printf("🔄 策略1: PNG转JPEG格式")
						req.Format = "jpeg"
						req.Quality = 85 // JPEG提高默认质量

						recaptureStart := time.Now()
						imageData, width, height, err = cm.captureScreen(req)
						recaptureTime := time.Since(recaptureStart)
						totalCaptureTime += recaptureTime

						if err != nil {
							log.Printf("❌ JPEG重新截图失败 (耗时: %v): %v", recaptureTime, err)
							continue
						}
						log.Printf("📷 JPEG重新截图完成 (新大小: %.1fKB, 耗时: %v)",
							float64(len(imageData))/1024, recaptureTime)
					} else if req.Quality > 50 {
						// 如果已经是JPEG，适度降低质量
						log.Printf("🔄 策略2: 降低JPEG质量 (%d -> %d)", req.Quality, req.Quality-10)
						req.Quality = req.Quality - 10

						recaptureStart := time.Now()
						imageData, width, height, err = cm.captureScreen(req)
						recaptureTime := time.Since(recaptureStart)
						totalCaptureTime += recaptureTime

						if err != nil {
							log.Printf("❌ 质量调整重新截图失败 (耗时: %v): %v", recaptureTime, err)
							continue
						}
						log.Printf("📷 质量调整完成 (质量: %d, 新大小: %.1fKB, 耗时: %v)",
							req.Quality, float64(len(imageData))/1024, recaptureTime)
					} else {
						log.Printf("⚠️  已达到最低质量，跳过重新压缩")
					}
				}
			}
			bandwidthTime = time.Since(bandwidthStart)
			log.Printf("📊 带宽检查完成 (耗时: %v)", bandwidthTime)

			// 🕒 步骤4: 更新缓存帧
			cacheStart := time.Now()
			if streamState.enableDiffDetection {
				lastImageData = make([]byte, len(imageData))
				copy(lastImageData, imageData)
			}
			cacheTime = time.Since(cacheStart)
			log.Printf("💾 缓存更新完成 (耗时: %v)", cacheTime)

			frameCount++

			// 🕒 步骤5: 构造和发送响应数据
			sendStart := time.Now()
			response := ScreenStreamDataResponse{
				TaskID:    taskID,
				Success:   true,
				StreamID:  fmt.Sprintf("stream_%d_%d", taskID, time.Now().Unix()),
				FrameData: imageData,
				Width:     width,
				Height:    height,
				Format:    req.Format,
				Size:      int64(len(imageData)),
				Timestamp: time.Now().Unix(),
			}

			cm.sendResp(Screenshot, StreamData, response)
			sendTime = time.Since(sendStart)
			totalSendTime += sendTime
			log.Printf("📡 数据发送完成 (耗时: %v)", sendTime)

			// 🕒 步骤6: 内存优化
			memStart := time.Now()
			if streamState.enableMemoryOptimization && frameCount%100 == 0 {
				// 清理大的缓存数据
				if len(lastImageData) > 1024*1024 { // 如果超过1MB
					lastImageData = nil
					log.Printf("🧹 清理大缓存帧 (>1MB)")
				}
			}
			memTime = time.Since(memStart)

			// 🕒 总体统计
			totalFrameTime := time.Since(frameStart)
			log.Printf("✅ 帧 #%d 处理完成 - 总耗时: %v (截图: %v, 差异: %v, 带宽: %v, 缓存: %v, 发送: %v, 内存: %v)",
				frameCount, totalFrameTime, captureTime, diffTime, bandwidthTime, cacheTime, sendTime, memTime)

			// 每10帧输出一次平均性能统计
			if frameCount%10 == 0 {
				avgCaptureTime := totalCaptureTime / time.Duration(frameCount)
				avgDiffTime := totalDiffTime / time.Duration(frameCount)
				avgSendTime := totalSendTime / time.Duration(frameCount)

				log.Printf("📊 最近10帧平均性能 - 截图: %v, 差异: %v, 发送: %v",
					avgCaptureTime, avgDiffTime, avgSendTime)
			}
		}
	}
}

// 处理屏幕流请求 (兼容旧版本)
func (cm *ConnectionManager) handleScreenStream(packet *Packet) {
	var req ScreenshotRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析屏幕流请求失败: %v", err)
		return
	}

	// 如果流未运行，发送单张截图
	if !streamState.isRunning {
		imageData, width, height, err := cm.captureScreen(&req)
		if err != nil {
			log.Printf("❌ 屏幕流截图失败: %v", err)
			return
		}

		cm.sendResp(Screenshot, Pic, ScreenshotResponse{
			TaskID:    req.TaskID,
			Success:   true,
			ImageData: imageData,
			Width:     width,
			Height:    height,
			Format:    req.Format,
			Size:      int64(len(imageData)),
			Timestamp: time.Now().Unix(),
		})
	}
}

// 获取显示器信息
func (cm *ConnectionManager) getMonitorInfo() []MonitorInfo {
	var monitors []MonitorInfo

	// 枚举所有显示器
	enumDisplayMonitors(0, nil, func(hMonitor uintptr, hdcMonitor uintptr, lprcMonitor *RECT, dwData uintptr) uintptr {
		var monitorInfo MONITORINFO
		monitorInfo.CbSize = uint32(unsafe.Sizeof(monitorInfo))

		if getMonitorInfo(hMonitor, &monitorInfo) {
			monitors = append(monitors, MonitorInfo{
				Index:   len(monitors),
				X:       int(monitorInfo.RcMonitor.Left),
				Y:       int(monitorInfo.RcMonitor.Top),
				Width:   int(monitorInfo.RcMonitor.Right - monitorInfo.RcMonitor.Left),
				Height:  int(monitorInfo.RcMonitor.Bottom - monitorInfo.RcMonitor.Top),
				Primary: (monitorInfo.DwFlags & MONITORINFOF_PRIMARY) != 0,
			})
		}
		return 1 // 继续枚举
	}, 0)

	// 如果枚举失败，至少返回主显示器信息
	if len(monitors) == 0 {
		// 🚀 使用DPI感知获取主显示器尺寸
		width, height := getRealScreenSize()

		monitors = append(monitors, MonitorInfo{
			Index:   0,
			X:       0,
			Y:       0,
			Width:   width,
			Height:  height,
			Primary: true,
		})
		log.Printf("🖥️ 回退到默认显示器信息: %dx%d", width, height)
	}

	return monitors
}

// 核心截图功能
func (cm *ConnectionManager) captureScreen(req *ScreenshotRequest) ([]byte, int, int, error) {
	var x, y, width, height int

	log.Printf("🔍 截图方法检测 - 请求类型: %d, 格式: %s, 质量: %d", req.Type, req.Format, req.Quality)

	// 🚀 智能优化2: 分辨率限制检查
	// 根据质量设置调整分辨率限制
	var maxWidth, maxHeight int
	if req.Quality >= 90 {
		// 高质量模式：允许更高分辨率
		maxWidth, maxHeight = 2560, 1440 // 2K分辨率
	} else if req.Quality >= 70 {
		// 中等质量模式：1080p
		maxWidth, maxHeight = 1920, 1080
	} else {
		// 低质量模式：720p
		maxWidth, maxHeight = 1280, 720
	}
	var needResize bool = false

	// 根据类型确定截图区域
	switch req.Type {
	case 0: // 全屏截图（虚拟屏幕）
		log.Printf("📸 尝试方法: DPI感知全屏截图")
		// 🚀 使用DPI感知的屏幕尺寸获取
		x, y, width, height = getRealVirtualScreenSize()
		// 如果虚拟屏幕尺寸为0，使用主显示器
		if width <= 0 || height <= 0 {
			x = 0
			y = 0
			width, height = getRealScreenSize()
			log.Printf("📸 回退到主显示器: %dx%d", width, height)
		} else {
			log.Printf("📸 使用虚拟屏幕: (%d,%d) %dx%d", x, y, width, height)
		}
	case 1: // 活动窗口截图
		// 使用专门的活动窗口截图函数
		return cm.captureActiveWindow(req)
	case 2: // 区域截图
		x = req.X
		y = req.Y
		width = req.Width
		height = req.Height
		if width <= 0 || height <= 0 {
			return nil, 0, 0, fmt.Errorf("无效的截图区域尺寸: %dx%d", width, height)
		}
		// 🚀 使用DPI感知验证区域是否在屏幕范围内
		virtualX, virtualY, virtualWidth, virtualHeight := getRealVirtualScreenSize()

		if x < virtualX || y < virtualY || x+width > virtualX+virtualWidth || y+height > virtualY+virtualHeight {
			log.Printf("⚠️ 警告: 指定区域可能超出屏幕范围")
			log.Printf("   请求区域: (%d,%d) %dx%d", x, y, width, height)
			log.Printf("   屏幕范围: (%d,%d) %dx%d", virtualX, virtualY, virtualWidth, virtualHeight)
		}
	case 3: // 指定显示器（保留兼容性）
		monitors := cm.getMonitorInfo()
		if req.MonitorID >= 0 && req.MonitorID < len(monitors) {
			monitor := monitors[req.MonitorID]
			x = monitor.X
			y = monitor.Y
			width = monitor.Width
			height = monitor.Height
		} else {
			return nil, 0, 0, fmt.Errorf("无效的显示器索引: %d，可用显示器数量: %d", req.MonitorID, len(monitors))
		}
	default:
		return nil, 0, 0, fmt.Errorf("不支持的截图类型: %d", req.Type)
	}

	// 验证截图区域
	if width <= 0 || height <= 0 {
		return nil, 0, 0, fmt.Errorf("截图区域尺寸无效: %dx%d", width, height)
	}

	log.Printf("🖼️ 截图区域: (%d,%d) %dx%d", x, y, width, height)

	// 获取屏幕DC
	screenDC := getDC(0)
	if screenDC == 0 {
		return nil, 0, 0, fmt.Errorf("无法获取屏幕DC，错误代码: %d", getLastError())
	}
	defer releaseDC(0, screenDC)

	// 创建兼容DC
	memDC := createCompatibleDC(screenDC)
	if memDC == 0 {
		return nil, 0, 0, fmt.Errorf("无法创建兼容DC，错误代码: %d", getLastError())
	}
	defer deleteDC(memDC)

	// 创建兼容位图
	hBitmap := createCompatibleBitmap(screenDC, width, height)
	if hBitmap == 0 {
		return nil, 0, 0, fmt.Errorf("无法创建兼容位图，错误代码: %d", getLastError())
	}
	defer deleteObject(hBitmap)

	// 选择位图到DC
	oldBitmap := selectObject(memDC, hBitmap)
	if oldBitmap == 0 {
		return nil, 0, 0, fmt.Errorf("无法选择位图到DC，错误代码: %d", getLastError())
	}
	defer selectObject(memDC, oldBitmap)

	// 执行位图复制
	if !bitBlt(memDC, 0, 0, width, height, screenDC, x, y, SRCCOPY) {
		return nil, 0, 0, fmt.Errorf("位图复制失败，错误代码: %d", getLastError())
	}

	// 转换为Go image
	img, err := cm.bitmapToImage(hBitmap, width, height)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("位图转换失败: %v", err)
	}

	// 🚀 智能优化2: 检查是否需要分辨率限制
	originalWidth := width
	originalHeight := height
	if originalWidth > maxWidth || originalHeight > maxHeight {
		needResize = true
		log.Printf("📏 分辨率优化: 原始 %dx%d > 1080p，需要缩放", originalWidth, originalHeight)
	}

	// 如果需要缩放，进行分辨率调整
	if needResize {
		img = cm.resizeImageTo1080p(img, originalWidth, originalHeight)
		width = img.Bounds().Dx()
		height = img.Bounds().Dy()
		log.Printf("📏 分辨率优化完成: 缩放到 %dx%d", width, height)
	}

	// 编码图片
	imageData, err := cm.encodeImage(img, req.Format, req.Quality)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
	}

	log.Printf("✅ 截图成功，图片大小: %d bytes", len(imageData))
	log.Printf("🖼️ 截图区域: (%d,%d) %dx%d", x, y, width, height)
	return imageData, width, height, nil
}

// 全屏截图实现
func (cm *ConnectionManager) captureFullScreen(req *ScreenshotRequest) ([]byte, int, int, error) {
	// 获取屏幕尺寸
	screenWidth := int(getSystemMetrics(0))  // SM_CXSCREEN
	screenHeight := int(getSystemMetrics(1)) // SM_CYSCREEN

	if screenWidth <= 0 || screenHeight <= 0 {
		return nil, 0, 0, fmt.Errorf("无法获取屏幕尺寸")
	}

	// 获取屏幕DC
	screenDC := getDC(0)
	if screenDC == 0 {
		return nil, 0, 0, fmt.Errorf("无法获取屏幕DC")
	}
	defer releaseDC(0, screenDC)

	// 创建兼容DC
	memDC := createCompatibleDC(screenDC)
	if memDC == 0 {
		return nil, 0, 0, fmt.Errorf("无法创建兼容DC")
	}
	defer deleteDC(memDC)

	// 创建兼容位图
	hBitmap := createCompatibleBitmap(screenDC, screenWidth, screenHeight)
	if hBitmap == 0 {
		return nil, 0, 0, fmt.Errorf("无法创建兼容位图")
	}
	defer deleteObject(hBitmap)

	// 选择位图到DC
	oldBitmap := selectObject(memDC, hBitmap)
	defer selectObject(memDC, oldBitmap)

	// 执行位图复制
	if !bitBlt(memDC, 0, 0, screenWidth, screenHeight, screenDC, 0, 0, 0x00CC0020) { // SRCCOPY
		return nil, 0, 0, fmt.Errorf("位图复制失败")
	}

	// 转换为Go image
	img, err := cm.bitmapToImage(hBitmap, screenWidth, screenHeight)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("位图转换失败: %v", err)
	}

	// 编码图片
	imageData, err := cm.encodeImage(img, req.Format, req.Quality)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
	}

	return imageData, screenWidth, screenHeight, nil
}

// 活动窗口截图实现
func (cm *ConnectionManager) captureActiveWindow(req *ScreenshotRequest) ([]byte, int, int, error) {
	log.Printf("📸 开始活动窗口截图")

	// 获取前台窗口句柄
	hwnd, _, _ := procGetForegroundWindow.Call()
	if hwnd == 0 {
		return nil, 0, 0, fmt.Errorf("无法获取前台窗口")
	}
	log.Printf("🪟 获取到前台窗口句柄: 0x%x", hwnd)

	// 获取窗口矩形
	var rect RECT
	ret, _, _ := procGetWindowRect.Call(hwnd, uintptr(unsafe.Pointer(&rect)))
	if ret == 0 {
		return nil, 0, 0, fmt.Errorf("无法获取窗口矩形")
	}

	// 计算窗口尺寸
	width := int(rect.Right - rect.Left)
	height := int(rect.Bottom - rect.Top)
	log.Printf("🪟 窗口尺寸: %dx%d, 位置: (%d,%d)", width, height, int(rect.Left), int(rect.Top))

	if width <= 0 || height <= 0 {
		return nil, 0, 0, fmt.Errorf("窗口尺寸无效")
	}

	// 🚀 方法1: 尝试使用PrintWindow API（推荐，支持硬件加速窗口）
	log.Printf("📸 尝试方法1: PrintWindow API")
	imageData, err := cm.captureWindowWithPrintWindow(hwnd, width, height, req)
	if err == nil {
		log.Printf("✅ 方法1成功: PrintWindow截图")
		return imageData, width, height, nil
	}
	log.Printf("❌ 方法1失败: %v", err)

	// 🚀 方法2: 回退到传统BitBlt方法
	log.Printf("📸 尝试方法2: 传统BitBlt方法")
	imageData, err = cm.captureWindowWithBitBlt(hwnd, width, height, req)
	if err == nil {
		log.Printf("✅ 方法2成功: BitBlt截图")
		return imageData, width, height, nil
	}
	log.Printf("❌ 方法2失败: %v", err)

	// 🚀 方法3: 最后回退到屏幕区域截图
	log.Printf("📸 尝试方法3: 屏幕区域截图")
	return cm.captureWindowRegionFromScreen(rect, req)
}

// 🚀 使用PrintWindow API截图（支持硬件加速窗口）
func (cm *ConnectionManager) captureWindowWithPrintWindow(hwnd uintptr, width, height int, req *ScreenshotRequest) ([]byte, error) {
	// 获取屏幕DC
	screenDC := getDC(0)
	if screenDC == 0 {
		return nil, fmt.Errorf("无法获取屏幕DC")
	}
	defer releaseDC(0, screenDC)

	// 创建兼容DC
	memDC := createCompatibleDC(screenDC)
	if memDC == 0 {
		return nil, fmt.Errorf("无法创建兼容DC")
	}
	defer deleteDC(memDC)

	// 创建兼容位图
	hBitmap := createCompatibleBitmap(screenDC, width, height)
	if hBitmap == 0 {
		return nil, fmt.Errorf("无法创建兼容位图")
	}
	defer deleteObject(hBitmap)

	// 选择位图到DC
	oldBitmap := selectObject(memDC, hBitmap)
	defer selectObject(memDC, oldBitmap)

	// 🚀 使用PrintWindow API - 支持硬件加速窗口
	ret, _, _ := procPrintWindow.Call(hwnd, memDC, PW_RENDERFULLCONTENT)
	if ret == 0 {
		// 尝试不带标志的PrintWindow
		ret, _, _ = procPrintWindow.Call(hwnd, memDC, 0)
		if ret == 0 {
			return nil, fmt.Errorf("PrintWindow失败")
		}
	}

	// 转换为Go image
	img, err := cm.bitmapToImage(hBitmap, width, height)
	if err != nil {
		return nil, fmt.Errorf("位图转换失败: %v", err)
	}

	// 编码图片
	imageData, err := cm.encodeImage(img, req.Format, req.Quality)
	if err != nil {
		return nil, fmt.Errorf("图片编码失败: %v", err)
	}

	return imageData, nil
}

// 🚀 使用传统BitBlt方法截图
func (cm *ConnectionManager) captureWindowWithBitBlt(hwnd uintptr, width, height int, req *ScreenshotRequest) ([]byte, error) {
	// 获取窗口DC
	hwndDC := getDC(hwnd)
	if hwndDC == 0 {
		return nil, fmt.Errorf("无法获取窗口DC")
	}
	defer releaseDC(hwnd, hwndDC)

	// 创建兼容DC
	memDC := createCompatibleDC(hwndDC)
	if memDC == 0 {
		return nil, fmt.Errorf("无法创建兼容DC")
	}
	defer deleteDC(memDC)

	// 创建兼容位图
	hBitmap := createCompatibleBitmap(hwndDC, width, height)
	if hBitmap == 0 {
		return nil, fmt.Errorf("无法创建兼容位图")
	}
	defer deleteObject(hBitmap)

	// 选择位图到DC
	oldBitmap := selectObject(memDC, hBitmap)
	defer selectObject(memDC, oldBitmap)

	// 执行位图复制
	if !bitBlt(memDC, 0, 0, width, height, hwndDC, 0, 0, SRCCOPY) {
		return nil, fmt.Errorf("位图复制失败")
	}

	// 转换为Go image
	img, err := cm.bitmapToImage(hBitmap, width, height)
	if err != nil {
		return nil, fmt.Errorf("位图转换失败: %v", err)
	}

	// 编码图片
	imageData, err := cm.encodeImage(img, req.Format, req.Quality)
	if err != nil {
		return nil, fmt.Errorf("图片编码失败: %v", err)
	}

	return imageData, nil
}

// 🚀 从屏幕区域截取窗口（最后的回退方案）
func (cm *ConnectionManager) captureWindowRegionFromScreen(rect RECT, req *ScreenshotRequest) ([]byte, int, int, error) {
	log.Printf("📸 使用屏幕区域截图作为回退方案")

	// 计算窗口在屏幕上的位置和尺寸
	x := int(rect.Left)
	y := int(rect.Top)
	width := int(rect.Right - rect.Left)
	height := int(rect.Bottom - rect.Top)

	log.Printf("🪟 窗口屏幕位置: (%d,%d) %dx%d", x, y, width, height)

	// 获取屏幕DC
	screenDC := getDC(0)
	if screenDC == 0 {
		return nil, 0, 0, fmt.Errorf("无法获取屏幕DC")
	}
	defer releaseDC(0, screenDC)

	// 创建兼容DC
	memDC := createCompatibleDC(screenDC)
	if memDC == 0 {
		return nil, 0, 0, fmt.Errorf("无法创建兼容DC")
	}
	defer deleteDC(memDC)

	// 创建兼容位图
	hBitmap := createCompatibleBitmap(screenDC, width, height)
	if hBitmap == 0 {
		return nil, 0, 0, fmt.Errorf("无法创建兼容位图")
	}
	defer deleteObject(hBitmap)

	// 选择位图到DC
	oldBitmap := selectObject(memDC, hBitmap)
	defer selectObject(memDC, oldBitmap)

	// 从屏幕复制窗口区域
	if !bitBlt(memDC, 0, 0, width, height, screenDC, x, y, SRCCOPY) {
		return nil, 0, 0, fmt.Errorf("屏幕区域复制失败")
	}

	// 转换为Go image
	img, err := cm.bitmapToImage(hBitmap, width, height)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("位图转换失败: %v", err)
	}

	// 编码图片
	imageData, err := cm.encodeImage(img, req.Format, req.Quality)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
	}

	log.Printf("✅ 方法3成功: 屏幕区域截图")
	return imageData, width, height, nil
}

// 指定区域截图实现
func (cm *ConnectionManager) captureRegion(req *ScreenshotRequest) ([]byte, int, int, error) {
	// 验证区域参数
	if req.Width <= 0 || req.Height <= 0 {
		return nil, 0, 0, fmt.Errorf("区域尺寸无效")
	}

	// 获取屏幕尺寸
	screenWidth := int(getSystemMetrics(0))  // SM_CXSCREEN
	screenHeight := int(getSystemMetrics(1)) // SM_CYSCREEN

	// 验证区域是否在屏幕范围内
	if req.X < 0 || req.Y < 0 || req.X+req.Width > screenWidth || req.Y+req.Height > screenHeight {
		return nil, 0, 0, fmt.Errorf("指定区域超出屏幕范围")
	}

	// 获取屏幕DC
	screenDC := getDC(0)
	if screenDC == 0 {
		return nil, 0, 0, fmt.Errorf("无法获取屏幕DC")
	}
	defer releaseDC(0, screenDC)

	// 创建兼容DC
	memDC := createCompatibleDC(screenDC)
	if memDC == 0 {
		return nil, 0, 0, fmt.Errorf("无法创建兼容DC")
	}
	defer deleteDC(memDC)

	// 创建兼容位图
	hBitmap := createCompatibleBitmap(screenDC, req.Width, req.Height)
	if hBitmap == 0 {
		return nil, 0, 0, fmt.Errorf("无法创建兼容位图")
	}
	defer deleteObject(hBitmap)

	// 选择位图到DC
	oldBitmap := selectObject(memDC, hBitmap)
	defer selectObject(memDC, oldBitmap)

	// 执行位图复制 (从指定区域)
	if !bitBlt(memDC, 0, 0, req.Width, req.Height, screenDC, req.X, req.Y, 0x00CC0020) { // SRCCOPY
		return nil, 0, 0, fmt.Errorf("位图复制失败")
	}

	// 转换为Go image
	img, err := cm.bitmapToImage(hBitmap, req.Width, req.Height)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("位图转换失败: %v", err)
	}

	// 编码图片
	imageData, err := cm.encodeImage(img, req.Format, req.Quality)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
	}

	return imageData, req.Width, req.Height, nil
}

// Windows API 包装函数
func getDC(hwnd uintptr) uintptr {
	ret, _, _ := procGetDC.Call(hwnd)
	return ret
}

func releaseDC(hwnd, hdc uintptr) bool {
	ret, _, _ := procReleaseDC.Call(hwnd, hdc)
	return ret != 0
}

func getWindowDC(hwnd uintptr) uintptr {
	ret, _, _ := procGetWindowDC.Call(hwnd)
	return ret
}

func getSystemMetrics(index int) int32 {
	ret, _, _ := procGetSystemMetrics.Call(uintptr(index))
	return int32(ret)
}

func createCompatibleDC(hdc uintptr) uintptr {
	ret, _, _ := procCreateCompatibleDC.Call(hdc)
	return ret
}

func deleteDC(hdc uintptr) bool {
	ret, _, _ := procDeleteDC.Call(hdc)
	return ret != 0
}

func createCompatibleBitmap(hdc uintptr, width, height int) uintptr {
	ret, _, _ := procCreateCompatibleBitmap.Call(hdc, uintptr(width), uintptr(height))
	return ret
}

func selectObject(hdc, obj uintptr) uintptr {
	ret, _, _ := procSelectObject.Call(hdc, obj)
	return ret
}

func deleteObject(obj uintptr) bool {
	ret, _, _ := procDeleteObject.Call(obj)
	return ret != 0
}

func bitBlt(hdcDest uintptr, x, y, width, height int, hdcSrc uintptr, x1, y1 int, rop uint32) bool {
	ret, _, _ := procBitBlt.Call(hdcDest, uintptr(x), uintptr(y), uintptr(width), uintptr(height), hdcSrc, uintptr(x1), uintptr(y1), uintptr(rop))
	return ret != 0
}

func stretchBlt(hdcDest uintptr, xDest, yDest, wDest, hDest int, hdcSrc uintptr, xSrc, ySrc, wSrc, hSrc int, rop uint32) bool {
	ret, _, _ := procStretchBlt.Call(
		hdcDest, uintptr(xDest), uintptr(yDest), uintptr(wDest), uintptr(hDest),
		hdcSrc, uintptr(xSrc), uintptr(ySrc), uintptr(wSrc), uintptr(hSrc), uintptr(rop))
	return ret != 0
}

func getDIBits(hdc, hbmp uintptr, uStartScan, cScanLines uint32, lpvBits uintptr, lpbi *BITMAPINFO, uUsage uint32) int32 {
	ret, _, _ := procGetDIBits.Call(hdc, hbmp, uintptr(uStartScan), uintptr(cScanLines), lpvBits, uintptr(unsafe.Pointer(lpbi)), uintptr(uUsage))
	return int32(ret)
}

func getForegroundWindow() uintptr {
	ret, _, _ := procGetForegroundWindow.Call()
	return ret
}

func getWindowRect(hwnd uintptr, rect *RECT) bool {
	ret, _, _ := procGetWindowRect.Call(hwnd, uintptr(unsafe.Pointer(rect)))
	return ret != 0
}

func getClientRect(hwnd uintptr, rect *RECT) bool {
	ret, _, _ := procGetClientRect.Call(hwnd, uintptr(unsafe.Pointer(rect)))
	return ret != 0
}

func clientToScreen(hwnd uintptr, point *POINT) bool {
	ret, _, _ := procClientToScreen.Call(hwnd, uintptr(unsafe.Pointer(point)))
	return ret != 0
}

func printWindow(hwnd, hdcBlt uintptr, nFlags uint32) bool {
	ret, _, _ := procPrintWindow.Call(hwnd, hdcBlt, uintptr(nFlags))
	return ret != 0
}

func isWindow(hwnd uintptr) bool {
	ret, _, _ := procIsWindow.Call(hwnd)
	return ret != 0
}

func isWindowVisible(hwnd uintptr) bool {
	ret, _, _ := procIsWindowVisible.Call(hwnd)
	return ret != 0
}

func getWindowText(hwnd uintptr) string {
	length, _, _ := procGetWindowTextLengthW.Call(hwnd)
	if length == 0 {
		return ""
	}

	buf := make([]uint16, length+1)
	procGetWindowTextW.Call(hwnd, uintptr(unsafe.Pointer(&buf[0])), length+1)
	return windows.UTF16ToString(buf)
}

func getLastError() uint32 {
	ret, _, _ := procGetLastError.Call()
	return uint32(ret)
}

// 显示器枚举回调函数类型
type MonitorEnumProc func(hMonitor uintptr, hdcMonitor uintptr, lprcMonitor *RECT, dwData uintptr) uintptr

func enumDisplayMonitors(hdc uintptr, lprcClip *RECT, lpfnEnum MonitorEnumProc, dwData uintptr) bool {
	callback := windows.NewCallback(func(hMonitor, hdcMonitor uintptr, lprcMonitor *RECT, dwData uintptr) uintptr {
		return lpfnEnum(hMonitor, hdcMonitor, lprcMonitor, dwData)
	})

	ret, _, _ := procEnumDisplayMonitors.Call(hdc, uintptr(unsafe.Pointer(lprcClip)), callback, dwData)
	return ret != 0
}

func getMonitorInfo(hMonitor uintptr, lpmi *MONITORINFO) bool {
	ret, _, _ := procGetMonitorInfoW.Call(hMonitor, uintptr(unsafe.Pointer(lpmi)))
	return ret != 0
}

// 位图转换为Go image
func (cm *ConnectionManager) bitmapToImage(hBitmap uintptr, width, height int) (image.Image, error) {
	// 获取屏幕DC
	screenDC := getDC(0)
	if screenDC == 0 {
		return nil, fmt.Errorf("无法获取屏幕DC，错误代码: %d", getLastError())
	}
	defer releaseDC(0, screenDC)

	// 获取位图信息
	bitmapInfo := &BITMAPINFO{
		BmiHeader: BITMAPINFOHEADER{
			BiSize:          40,
			BiWidth:         int32(width),
			BiHeight:        -int32(height), // 负值表示自上而下
			BiPlanes:        1,
			BiBitCount:      32, // 32位BGRA
			BiCompression:   0,  // BI_RGB
			BiSizeImage:     0,
			BiXPelsPerMeter: 0,
			BiYPelsPerMeter: 0,
			BiClrUsed:       0,
			BiClrImportant:  0,
		},
	}

	// 计算图像数据大小 (32位，4字节每像素)
	dataSize := width * height * 4
	bitmapData := make([]byte, dataSize)

	// 获取位图数据
	ret := getDIBits(screenDC, hBitmap, 0, uint32(height), uintptr(unsafe.Pointer(&bitmapData[0])), bitmapInfo, DIB_RGB_COLORS)
	if ret == 0 {
		return nil, fmt.Errorf("获取位图数据失败，错误代码: %d", getLastError())
	}

	// 创建RGBA图像
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 转换BGRA到RGBA (Windows位图是BGRA格式)
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			srcOffset := (y*width + x) * 4
			dstOffset := (y*width + x) * 4

			if srcOffset+3 < len(bitmapData) && dstOffset+3 < len(img.Pix) {
				// BGRA -> RGBA
				img.Pix[dstOffset+0] = bitmapData[srcOffset+2] // R
				img.Pix[dstOffset+1] = bitmapData[srcOffset+1] // G
				img.Pix[dstOffset+2] = bitmapData[srcOffset+0] // B
				img.Pix[dstOffset+3] = 255                     // A (设置为不透明)
			}
		}
	}

	return img, nil
}

// 图片编码
func (cm *ConnectionManager) encodeImage(img image.Image, format string, quality int) ([]byte, error) {
	// 使用内存池获取压缩缓冲区
	compressBuf := cm.memoryPool.GetCompressBuffer()
	defer cm.memoryPool.PutCompressBuffer(compressBuf)

	// 创建基于内存池缓冲区的Buffer
	buf := bytes.NewBuffer(compressBuf[:0])

	switch format {
	case "png":
		if err := png.Encode(buf, img); err != nil {
			return nil, fmt.Errorf("PNG编码失败: %v", err)
		}
	case "jpeg", "jpg":
		// 确保质量值在有效范围内
		if quality < 1 {
			quality = 85 // 提高默认质量
		} else if quality > 100 {
			quality = 100
		}

		// 对于高质量截图，使用更好的编码参数
		options := &jpeg.Options{
			Quality: quality,
		}

		if err := jpeg.Encode(buf, img, options); err != nil {
			return nil, fmt.Errorf("JPEG编码失败: %v", err)
		}
	default:
		return nil, fmt.Errorf("不支持的图片格式: %s", format)
	}

	// 创建返回副本
	result := make([]byte, buf.Len())
	copy(result, buf.Bytes())
	return result, nil
}

// 计算图像差异百分比
func (cm *ConnectionManager) calculateImageDifference(img1, img2 []byte) float64 {
	if len(img1) != len(img2) {
		return 100.0 // 完全不同
	}

	if len(img1) == 0 {
		return 0.0
	}

	diffCount := 0
	totalBytes := len(img1)

	// 简单的字节级差异检测
	for i := 0; i < totalBytes; i++ {
		if img1[i] != img2[i] {
			diffCount++
		}
	}

	// 计算差异百分比
	diffPercent := float64(diffCount) / float64(totalBytes) * 100.0
	return diffPercent
}

// MonitorInfo 显示器信息 (与Linux版本保持一致)
type MonitorInfo struct {
	Index   int  `json:"index"`   // 显示器索引
	X       int  `json:"x"`       // X坐标
	Y       int  `json:"y"`       // Y坐标
	Width   int  `json:"width"`   // 宽度
	Height  int  `json:"height"`  // 高度
	Primary bool `json:"primary"` // 是否主显示器
}

type MonitorListRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
}

// MonitorListResponse 显示器列表响应
type MonitorListResponse struct {
	TaskID   uint64        `json:"task_id"`  // 任务ID
	Success  bool          `json:"success"`  // 操作是否成功
	Monitors []MonitorInfo `json:"monitors"` // 显示器列表
	Error    string        `json:"error"`    // 错误信息
	Count    int           `json:"count"`    // 显示器数量
}

// 获取显示器信息

// 处理获取显示器列表请求
func (cm *ConnectionManager) handleMonitorList(packet *Packet) {
	log.Printf("🖥️ 开始获取显示器列表")

	// 解析请求以获取TaskID
	var req MonitorListRequest
	var taskID uint64 = 0
	if packet.PacketData.Data != nil && len(packet.PacketData.Data) > 0 {
		if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
			log.Printf("⚠️ 解析显示器列表请求失败: %v", err)
		} else {
			taskID = req.TaskID
			log.Printf("📋 解析到TaskID: %d", taskID)
		}
	}

	// 获取显示器信息
	monitors := cm.getMonitorInfo()
	if len(monitors) == 0 {
		log.Printf("❌ 获取显示器信息失败")
		cm.sendResp(Screenshot, MonitorList, MonitorListResponse{
			TaskID:  taskID,
			Success: false,
			Error:   "获取显示器信息失败",
			Count:   0,
		})
		return
	}

	// 发送成功响应
	cm.sendResp(Screenshot, MonitorList, MonitorListResponse{
		TaskID:   taskID,
		Success:  true,
		Monitors: monitors,
		Count:    len(monitors),
	})

	log.Printf("✅ 显示器列表获取完成 - 数量: %d, TaskID: %d", len(monitors), taskID)
}

// 🚀 智能分辨率缩放到1080p
func (cm *ConnectionManager) resizeImageTo1080p(img image.Image, originalWidth, originalHeight int) image.Image {
	maxWidth, maxHeight := 1920, 1080

	// 计算缩放比例，保持宽高比
	scaleX := float64(maxWidth) / float64(originalWidth)
	scaleY := float64(maxHeight) / float64(originalHeight)
	scale := scaleX
	if scaleY < scaleX {
		scale = scaleY
	}

	// 计算新的尺寸
	newWidth := int(float64(originalWidth) * scale)
	newHeight := int(float64(originalHeight) * scale)

	log.Printf("📏 缩放参数: 原始(%dx%d) -> 目标(%dx%d), 缩放比例: %.3f",
		originalWidth, originalHeight, newWidth, newHeight, scale)

	// 创建新的RGBA图像
	newImg := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	// 简单的最近邻缩放算法
	bounds := img.Bounds()
	for y := 0; y < newHeight; y++ {
		for x := 0; x < newWidth; x++ {
			// 计算源像素位置
			srcX := int(float64(x) / scale)
			srcY := int(float64(y) / scale)

			// 确保不超出边界
			if srcX >= bounds.Max.X {
				srcX = bounds.Max.X - 1
			}
			if srcY >= bounds.Max.Y {
				srcY = bounds.Max.Y - 1
			}

			// 复制像素
			newImg.Set(x, y, img.At(srcX+bounds.Min.X, srcY+bounds.Min.Y))
		}
	}

	return newImg
}

// 🚀 DPI感知辅助函数

// 设置进程DPI感知模式
func setProcessDpiAwarenessContext(dpiContext uintptr) bool {
	ret, _, _ := procSetProcessDpiAwarenessContext.Call(dpiContext)
	return ret != 0
}

// 获取系统DPI
func getDpiForSystem() uint32 {
	ret, _, _ := procGetDpiForSystem.Call()
	return uint32(ret)
}

// 获取窗口DPI
func getDpiForWindow(hwnd uintptr) uint32 {
	ret, _, _ := procGetDpiForWindow.Call(hwnd)
	return uint32(ret)
}

// 获取DPI感知的系统度量值
func getSystemMetricsForDpi(nIndex int, dpi uint32) int {
	ret, _, _ := procGetSystemMetricsForDpi.Call(uintptr(nIndex), uintptr(dpi))
	return int(ret)
}

// 🚀 获取真实屏幕尺寸（考虑DPI缩放）
func getRealScreenSize() (int, int) {
	// 获取系统DPI
	systemDpi := getDpiForSystem()
	log.Printf("🖥️ 系统DPI: %d", systemDpi)

	// 如果支持DPI感知API，使用它们
	if procGetSystemMetricsForDpi.Find() == nil {
		width := getSystemMetricsForDpi(SM_CXSCREEN, systemDpi)
		height := getSystemMetricsForDpi(SM_CYSCREEN, systemDpi)
		log.Printf("🖥️ DPI感知屏幕尺寸: %dx%d", width, height)
		return width, height
	}

	// 回退到传统方法
	width := int(getSystemMetrics(SM_CXSCREEN))
	height := int(getSystemMetrics(SM_CYSCREEN))
	log.Printf("🖥️ 传统屏幕尺寸: %dx%d", width, height)

	// 如果DPI不是96（100%缩放），尝试计算真实尺寸
	if systemDpi != 96 {
		scaleFactor := float64(systemDpi) / 96.0
		realWidth := int(float64(width) * scaleFactor)
		realHeight := int(float64(height) * scaleFactor)
		log.Printf("🖥️ DPI缩放计算: %.2f倍, 真实尺寸: %dx%d", scaleFactor, realWidth, realHeight)
		return realWidth, realHeight
	}

	return width, height
}

// 🚀 获取真实虚拟屏幕尺寸
func getRealVirtualScreenSize() (int, int, int, int) {
	systemDpi := getDpiForSystem()

	if procGetSystemMetricsForDpi.Find() == nil {
		x := getSystemMetricsForDpi(SM_XVIRTUALSCREEN, systemDpi)
		y := getSystemMetricsForDpi(SM_YVIRTUALSCREEN, systemDpi)
		width := getSystemMetricsForDpi(SM_CXVIRTUALSCREEN, systemDpi)
		height := getSystemMetricsForDpi(SM_CYVIRTUALSCREEN, systemDpi)
		log.Printf("🖥️ DPI感知虚拟屏幕: (%d,%d) %dx%d", x, y, width, height)
		return x, y, width, height
	}

	// 回退到传统方法
	x := int(getSystemMetrics(SM_XVIRTUALSCREEN))
	y := int(getSystemMetrics(SM_YVIRTUALSCREEN))
	width := int(getSystemMetrics(SM_CXVIRTUALSCREEN))
	height := int(getSystemMetrics(SM_CYVIRTUALSCREEN))

	// DPI缩放计算
	if systemDpi != 96 {
		scaleFactor := float64(systemDpi) / 96.0
		x = int(float64(x) * scaleFactor)
		y = int(float64(y) * scaleFactor)
		width = int(float64(width) * scaleFactor)
		height = int(float64(height) * scaleFactor)
		log.Printf("🖥️ DPI缩放虚拟屏幕: (%d,%d) %dx%d", x, y, width, height)
	}

	return x, y, width, height
}
