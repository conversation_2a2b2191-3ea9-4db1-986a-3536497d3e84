//go:build linux
// +build linux

package common

import (
	"encoding/json"
	"fmt"
	"log"
	"os/exec"
	"strings"

	"github.com/shirou/gopsutil/v3/process"
)

// handleProcessKill 处理终止进程请求
func (cm *ConnectionManager) handleProcessKill(packet *Packet) {
	var req ProcessKillRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessKillResponse{
		TaskID:  0,
		Success: false,
		PID:     0,
		Error:   "",
	}
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析终止进程请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "解析请求失败"
		cm.sendProcessResponse(ProcessKill, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID
	errorResp.PID = req.PID

	err := cm.killProcess(req.PID, req.Force)
	if err != nil {
		log.Printf("终止进程失败: %v", err)
		// 构造错误响应
		response := ProcessKillResponse{
			TaskID:  req.TaskID,
			Success: false,
			PID:     req.PID,
			Error:   fmt.Sprintf("终止进程失败: %v", err),
		}
		cm.sendProcessResponse(ProcessKill, false, fmt.Sprintf("终止进程失败: %v", err), response)
		return
	}

	// 构造成功响应
	response := ProcessKillResponse{
		TaskID:  req.TaskID,
		Success: true,
		PID:     req.PID,
		Error:   "",
	}
	cm.sendProcessResponse(ProcessKill, true, fmt.Sprintf("进程 %d 已终止", req.PID), response)
}

// handleProcessStart 处理启动进程请求
func (cm *ConnectionManager) handleProcessStart(packet *Packet) {
	var req ProcessStartRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessStartResponse{
		TaskID:     0,
		Success:    false,
		PID:        0,
		Error:      "",
		ExitCode:   0,
		Output:     "",
		Executable: "",
	}
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析启动进程请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "解析请求失败"
		cm.sendProcessResponse(ProcessStart, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID
	errorResp.Executable = req.Command

	pid, err := cm.startProcess(req)
	if err != nil {
		log.Printf("启动进程失败: %v", err)
		startResp := ProcessStartResponse{
			TaskID:     req.TaskID,
			Success:    false,
			PID:        0,
			Error:      fmt.Sprintf("启动进程失败: %v", err),
			ExitCode:   0,
			Output:     "",
			Executable: req.Command,
		}
		cm.sendProcessResponse(ProcessStart, false, fmt.Sprintf("启动进程失败: %v", err), startResp)
		return
	}

	startResp := ProcessStartResponse{
		TaskID:     req.TaskID,
		Success:    true,
		PID:        int32(pid),
		Error:      "",
		ExitCode:   0,
		Output:     "",
		Executable: req.Command,
	}
	cm.sendProcessResponse(ProcessStart, true, fmt.Sprintf("进程启动成功，PID: %d", pid), startResp)
}

// handleProcessDetails 处理获取进程详情请求
func (cm *ConnectionManager) handleProcessDetails(packet *Packet) {
	var req ProcessDetailsRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessDetailsResponse{
		TaskID:      0,
		Success:     false,
		Process:     nil,
		Modules:     []interface{}{},
		Connections: []interface{}{},
		OpenFiles:   []interface{}{},
		Error:       "",
	}
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析进程详情请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "解析请求失败"
		cm.sendProcessResponse(ProcessDetails, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	ProcessFullInfo, err := cm.getProcessDetails(req.PID)
	if err != nil {
		log.Printf("获取进程详情失败: %v", err)
		detailsResp := ProcessDetailsResponse{
			TaskID:      req.TaskID,
			Success:     false,
			Process:     nil,
			Modules:     []interface{}{},
			Connections: []interface{}{},
			OpenFiles:   []interface{}{},
			Error:       fmt.Sprintf("获取进程详情失败: %v", err),
		}
		cm.sendProcessResponse(ProcessDetails, false, fmt.Sprintf("获取进程详情失败: %v", err), detailsResp)
		return
	}

	detailsResp := ProcessDetailsResponse{
		TaskID:      req.TaskID,
		Success:     true,
		Process:     ProcessFullInfo,
		Modules:     []interface{}{},
		Connections: []interface{}{},
		OpenFiles:   []interface{}{},
		Error:       "",
	}
	cm.sendProcessResponse(ProcessDetails, true, "获取进程详情成功", detailsResp)
}

// handleProcessSuspend 处理挂起进程请求
func (cm *ConnectionManager) handleProcessSuspend(packet *Packet) {
	var req ProcessSuspendRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessSuspendResponse{
		TaskID:  0,
		Success: false,
		PID:     0,
		Error:   "",
	}
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析挂起进程请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "解析请求失败"
		cm.sendProcessResponse(ProcessSuspend, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID
	errorResp.PID = req.PID

	err := cm.suspendProcess(req.PID)
	if err != nil {
		log.Printf("挂起进程失败: %v", err)
		response := ProcessSuspendResponse{
			TaskID:  req.TaskID,
			Success: false,
			PID:     req.PID,
			Error:   fmt.Sprintf("挂起进程失败: %v", err),
		}
		cm.sendProcessResponse(ProcessSuspend, false, fmt.Sprintf("挂起进程失败: %v", err), response)
		return
	}

	response := ProcessSuspendResponse{
		TaskID:  req.TaskID,
		Success: true,
		PID:     req.PID,
		Error:   "",
	}
	cm.sendProcessResponse(ProcessSuspend, true, fmt.Sprintf("进程 %d 已挂起", req.PID), response)
}

// handleProcessResume 处理恢复进程请求
func (cm *ConnectionManager) handleProcessResume(packet *Packet) {
	var req ProcessResumeRequest
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessResumeResponse{
		TaskID:  0,
		Success: false,
		PID:     0,
		Error:   "",
	}
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析恢复进程请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.Error = "解析请求失败"
		cm.sendProcessResponse(ProcessResume, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID
	errorResp.PID = req.PID

	err := cm.resumeProcess(req.PID)
	if err != nil {
		log.Printf("恢复进程失败: %v", err)
		response := ProcessResumeResponse{
			TaskID:  req.TaskID,
			Success: false,
			PID:     req.PID,
			Error:   fmt.Sprintf("恢复进程失败: %v", err),
		}
		cm.sendProcessResponse(ProcessResume, false, fmt.Sprintf("恢复进程失败: %v", err), response)
		return
	}

	response := ProcessResumeResponse{
		TaskID:  req.TaskID,
		Success: true,
		PID:     req.PID,
		Error:   "",
	}
	cm.sendProcessResponse(ProcessResume, true, fmt.Sprintf("进程 %d 已恢复", req.PID), response)
}

// getProcessDetails 获取进程详细信息
func (cm *ConnectionManager) getProcessDetails(pid int32) (*ProcessFullInfo, error) {
	p, err := process.NewProcess(pid)
	if err != nil {
		return nil, err
	}

	// 使用优化版本，详情模式
	return cm.getProcessFullInfoOptimized(p, true)
}

// killProcess 终止进程
func (cm *ConnectionManager) killProcess(pid int32, force bool) error {
	p, err := process.NewProcess(pid)
	if err != nil {
		return err
	}

	if force {
		return p.Kill()
	} else {
		return p.Terminate()
	}
}

// startProcess 启动进程
func (cm *ConnectionManager) startProcess(req ProcessStartRequest) (int, error) {
	var cmd *exec.Cmd

	if req.Args != "" {
		args := strings.Fields(req.Args)
		cmd = exec.Command(req.Command, args...)
	} else {
		cmd = exec.Command(req.Command)
	}

	if req.WorkDir != "" {
		cmd.Dir = req.WorkDir
	}

	// Linux下的提权处理
	if req.RunAsAdmin {
		// 使用sudo执行
		originalArgs := cmd.Args
		cmd = exec.Command("sudo", append([]string{"-n"}, originalArgs...)...)
	}

	err := cmd.Start()
	if err != nil {
		return 0, err
	}

	return cmd.Process.Pid, nil
}

// suspendProcess 挂起进程
func (cm *ConnectionManager) suspendProcess(pid int32) error {
	p, err := process.NewProcess(pid)
	if err != nil {
		return err
	}

	return p.Suspend()
}

// resumeProcess 恢复进程
func (cm *ConnectionManager) resumeProcess(pid int32) error {
	p, err := process.NewProcess(pid)
	if err != nil {
		return err
	}

	return p.Resume()
}
