//go:build linux
// +build linux

package common

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"

	psutilNet "github.com/shirou/gopsutil/v3/net"
	"github.com/shirou/gopsutil/v3/process"
)

// 网络监控并发控制
var (
	networkMutex        sync.Mutex // 网络监控请求互斥锁
	isNetworkProcessing bool       // 是否正在处理网络监控请求
)

// 网络连接数量限制
const (
	MaxNetworkConnections = 1000 // 最大返回连接数
)

// 网络监控请求结构体
type NetworkStatsRequest struct {
	TaskID uint64 `json:"task_id"`
}

type NetworkInterfacesRequest struct {
	TaskID uint64 `json:"task_id"`
}

type NetworkConnectionsRequest struct {
	TaskID   uint64 `json:"task_id"`
	Protocol string `json:"protocol"`
	State    string `json:"state"`
}

type CloseConnectionRequest struct {
	TaskID       uint64 `json:"task_id"`
	ConnectionID string `json:"connection_id"`
	LocalAddr    string `json:"local_addr"`
	LocalPort    int    `json:"local_port"`
	RemoteAddr   string `json:"remote_addr"`
	RemotePort   int    `json:"remote_port"`
	Protocol     string `json:"protocol"`
}

// 网络监控数据结构体
type NetworkStats struct {
	UploadSpeed       float64 `json:"upload_speed"`
	DownloadSpeed     float64 `json:"download_speed"`
	ActiveConnections int     `json:"active_connections"`
	PacketLoss        float64 `json:"packet_loss"`
	TotalBytesSent    uint64  `json:"total_bytes_sent"`
	TotalBytesRecv    uint64  `json:"total_bytes_recv"`
	Timestamp         int64   `json:"timestamp"`
}

type NetInterface struct {
	Name          string `json:"name"`
	Type          string `json:"type"`
	Status        string `json:"status"`
	IPAddress     string `json:"ip_address"`
	MACAddress    string `json:"mac_address"`
	Speed         uint64 `json:"speed"`
	BytesSent     uint64 `json:"bytes_sent"`
	BytesReceived uint64 `json:"bytes_received"`
	PacketsSent   uint64 `json:"packets_sent"`
	PacketsRecv   uint64 `json:"packets_recv"`
	ErrorsIn      uint64 `json:"errors_in"`
	ErrorsOut     uint64 `json:"errors_out"`
	DropsIn       uint64 `json:"drops_in"`
	DropsOut      uint64 `json:"drops_out"`
}

type NetworkConnection struct {
	ID              string    `json:"id"`
	Protocol        string    `json:"protocol"`
	LocalAddress    string    `json:"local_address"`
	LocalPort       int       `json:"local_port"`
	RemoteAddress   string    `json:"remote_address"`
	RemotePort      int       `json:"remote_port"`
	State           string    `json:"state"`
	ProcessName     string    `json:"process_name"`
	PID             int       `json:"pid"`
	EstablishedTime time.Time `json:"established_time"`
	Duration        string    `json:"duration"`
	BytesSent       uint64    `json:"bytes_sent"`
	BytesReceived   uint64    `json:"bytes_received"`
}

// 响应结构体
type NetworkStatsResponse struct {
	TaskID  uint64       `json:"task_id"`
	Success bool         `json:"success"`
	Error   string       `json:"error"`
	Stats   NetworkStats `json:"stats"`
}

type NetworkInterfacesResponse struct {
	TaskID     uint64         `json:"task_id"`
	Success    bool           `json:"success"`
	Error      string         `json:"error"`
	Interfaces []NetInterface `json:"interfaces"`
}

type NetworkConnectionsResponse struct {
	TaskID      uint64              `json:"task_id"`
	Success     bool                `json:"success"`
	Error       string              `json:"error"`
	Connections []NetworkConnection `json:"connections"`
	Total       int                 `json:"total"`
}

type CloseConnectionResponse struct {
	TaskID  uint64 `json:"task_id"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

// handleNetworkRequest 处理网络监控请求的主入口
func (cm *ConnectionManager) handleNetworkRequest(packet *Packet) {
	// 检查是否有其他网络监控请求正在处理
	networkMutex.Lock()
	if isNetworkProcessing {
		networkMutex.Unlock()
		log.Printf("⚠️ 网络监控请求正在处理中，跳过当前请求: Code=%d", packet.Header.Code)
		// 发送忙碌响应
		cm.sendBusyResponse(packet.Header.Code)
		return
	}
	isNetworkProcessing = true
	networkMutex.Unlock()

	// 处理完成后释放锁
	defer func() {
		networkMutex.Lock()
		isNetworkProcessing = false
		networkMutex.Unlock()
	}()

	switch packet.Header.Code {
	case NetStatsCmd:
		cm.handleNetworkStats(packet)
	case NetInterfacesCmd:
		cm.handleNetworkInterfaces(packet)
	case NetConnectionsCmd:
		cm.handleNetworkConnections(packet)
	case NetCloseConnCmd:
		cm.handleCloseConnection(packet)
	default:
		log.Printf("未知的网络监控操作代码: %d", packet.Header.Code)
	}
}

// handleNetworkStats 处理网络统计信息请求
func (cm *ConnectionManager) handleNetworkStats(packet *Packet) {
	log.Printf("🌐 开始处理网络统计信息请求")

	// 创建错误响应结构体
	errorResp := NetworkStatsResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req NetworkStatsRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络统计信息请求失败: %v", err)
		cm.sendNetworkResponse(NetStatsCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	// 获取网络统计信息
	stats, err := cm.getNetworkStats()
	if err != nil {
		log.Printf("❌ 获取网络统计信息失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendNetworkResponse(NetStatsCmd, errorResp)
		return
	}

	// 创建成功响应
	response := NetworkStatsResponse{
		TaskID:  req.TaskID,
		Success: true,
		Stats:   *stats,
	}

	cm.sendNetworkResponse(NetStatsCmd, response)
	log.Printf("✅ 网络统计信息请求处理完成")
}

// handleNetworkInterfaces 处理网络接口信息请求
func (cm *ConnectionManager) handleNetworkInterfaces(packet *Packet) {
	log.Printf("🌐 开始处理网络接口信息请求")

	errorResp := NetworkInterfacesResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req NetworkInterfacesRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络接口信息请求失败: %v", err)
		cm.sendNetworkResponse(NetInterfacesCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	// 获取网络接口信息
	interfaces, err := cm.getNetworkInterfaces()
	if err != nil {
		log.Printf("❌ 获取网络接口信息失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendNetworkResponse(NetInterfacesCmd, errorResp)
		return
	}

	// 创建成功响应
	response := NetworkInterfacesResponse{
		TaskID:     req.TaskID,
		Success:    true,
		Interfaces: interfaces,
	}

	cm.sendNetworkResponse(NetInterfacesCmd, response)
	log.Printf("✅ 网络接口信息请求处理完成")
}

// handleNetworkConnections 处理网络连接信息请求
func (cm *ConnectionManager) handleNetworkConnections(packet *Packet) {
	log.Printf("🌐 开始处理网络连接信息请求")

	errorResp := NetworkConnectionsResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req NetworkConnectionsRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络连接信息请求失败: %v", err)
		cm.sendNetworkResponse(NetConnectionsCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	// 获取网络连接信息
	connections, err := cm.getNetworkConnections(req.Protocol, req.State)
	if err != nil {
		log.Printf("❌ 获取网络连接信息失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendNetworkResponse(NetConnectionsCmd, errorResp)
		return
	}

	// 创建成功响应
	response := NetworkConnectionsResponse{
		TaskID:      req.TaskID,
		Success:     true,
		Connections: connections,
		Total:       len(connections),
	}

	cm.sendNetworkResponse(NetConnectionsCmd, response)
	log.Printf("✅ 网络连接信息请求处理完成")
}

// handleCloseConnection 处理关闭网络连接请求
func (cm *ConnectionManager) handleCloseConnection(packet *Packet) {
	log.Printf("🌐 开始处理关闭网络连接请求")

	errorResp := CloseConnectionResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req CloseConnectionRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析关闭网络连接请求失败: %v", err)
		cm.sendNetworkResponse(NetCloseConnCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	// 关闭网络连接
	err := cm.closeNetworkConnection(req.ConnectionID, req.Protocol, req.LocalAddr, req.LocalPort, req.RemoteAddr, req.RemotePort)
	if err != nil {
		log.Printf("❌ 关闭网络连接失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendNetworkResponse(NetCloseConnCmd, errorResp)
		return
	}

	// 创建成功响应
	response := CloseConnectionResponse{
		TaskID:  req.TaskID,
		Success: true,
		Message: "网络连接关闭成功",
	}

	cm.sendNetworkResponse(NetCloseConnCmd, response)
	log.Printf("✅ 关闭网络连接请求处理完成")
}

// sendNetworkResponse 发送网络监控响应
func (cm *ConnectionManager) sendNetworkResponse(code uint8, response interface{}) {
	cm.sendResp(Network, code, response)
}

// sendBusyResponse 发送忙碌响应
func (cm *ConnectionManager) sendBusyResponse(code uint8) {
	var response interface{}

	switch code {
	case NetStatsCmd:
		response = NetworkStatsResponse{
			TaskID:  0,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetInterfacesCmd:
		response = NetworkInterfacesResponse{
			TaskID:  0,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetConnectionsCmd:
		response = NetworkConnectionsResponse{
			TaskID:  0,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetCloseConnCmd:
		response = CloseConnectionResponse{
			TaskID:  0,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	default:
		return
	}

	cm.sendNetworkResponse(code, response)
}

// getNetworkStats 获取网络统计信息
func (cm *ConnectionManager) getNetworkStats() (*NetworkStats, error) {
	stats := &NetworkStats{
		Timestamp: time.Now().Unix(),
	}

	// 读取 /proc/net/dev 获取网络接口统计
	data, err := os.ReadFile("/proc/net/dev")
	if err != nil {
		return nil, fmt.Errorf("读取网络统计失败: %v", err)
	}

	lines := strings.Split(string(data), "\n")
	var totalBytesSent, totalBytesRecv uint64

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, ":") && !strings.HasPrefix(line, "Inter-") && !strings.HasPrefix(line, "face") {
			parts := strings.Fields(line)
			if len(parts) >= 10 {
				// 接收字节数在第2列，发送字节数在第10列
				if recv, err := strconv.ParseUint(parts[1], 10, 64); err == nil {
					totalBytesRecv += recv
				}
				if sent, err := strconv.ParseUint(parts[9], 10, 64); err == nil {
					totalBytesSent += sent
				}
			}
		}
	}

	stats.TotalBytesSent = totalBytesSent
	stats.TotalBytesRecv = totalBytesRecv

	// 获取活跃连接数
	connections, err := cm.getNetworkConnections("ALL", "")
	if err == nil {
		stats.ActiveConnections = len(connections)
	}

	// 🚀 使用真实的速度计算方法（学习dashboard实现）
	// 优先使用gopsutil获取真实的网络统计数据
	realSent, realRecv, err := cm.getRealNetworkStats()
	if err != nil {
		log.Printf("⚠️ 获取真实网络统计失败，使用/proc/net/dev数据: %v", err)
		realSent, realRecv = totalBytesSent, totalBytesRecv
	}

	uploadSpeed, downloadSpeed := cm.calculateRealNetworkSpeed(realSent, realRecv)
	stats.UploadSpeed = uploadSpeed
	stats.DownloadSpeed = downloadSpeed
	stats.TotalBytesSent = realSent
	stats.TotalBytesRecv = realRecv

	return stats, nil
}

// getNetworkInterfaces 获取网络接口信息（流式版本）
func (cm *ConnectionManager) getNetworkInterfaces() ([]NetInterface, error) {
	log.Printf("🔍 getNetworkInterfaces: 开始获取网络接口信息")

	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("获取网络接口失败: %v", err)
	}

	log.Printf("📊 getNetworkInterfaces: 找到%d个网络接口", len(interfaces))
	var result []NetInterface

	for i, iface := range interfaces {
		log.Printf("🌐 getNetworkInterfaces: 处理接口 %d/%d: %s", i+1, len(interfaces), iface.Name)

		netIface := NetInterface{
			Name:       iface.Name,
			MACAddress: iface.HardwareAddr.String(),
		}

		// 获取接口状态
		if iface.Flags&net.FlagUp != 0 {
			netIface.Status = "Up"
		} else {
			netIface.Status = "Down"
		}

		// 获取接口类型
		if iface.Flags&net.FlagLoopback != 0 {
			netIface.Type = "Loopback"
		} else if strings.HasPrefix(iface.Name, "eth") {
			netIface.Type = "Ethernet"
		} else if strings.HasPrefix(iface.Name, "wlan") || strings.HasPrefix(iface.Name, "wifi") {
			netIface.Type = "Wireless"
		} else {
			netIface.Type = "Other"
		}

		// 获取IP地址
		addrs, err := iface.Addrs()
		if err == nil {
			for _, addr := range addrs {
				if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
					if ipnet.IP.To4() != nil {
						netIface.IPAddress = ipnet.IP.String()
						break
					}
				}
			}
		}

		// 从 /proc/net/dev 获取统计信息
		cm.getInterfaceStats(&netIface)

		result = append(result, netIface)

		// 🚀 流式传输：每处理完一个接口就发送一次进度更新
		if i < len(interfaces)-1 { // 不是最后一个接口
			cm.sendInterfaceProgress(i+1, len(interfaces), netIface)
		}
	}

	log.Printf("✅ getNetworkInterfaces: 网络接口信息获取完成，共%d个接口", len(result))
	return result, nil
}

// sendInterfaceProgress 发送接口处理进度
func (cm *ConnectionManager) sendInterfaceProgress(current, total int, iface NetInterface) {
	log.Printf("📤 发送接口进度: %d/%d - %s", current, total, iface.Name)

	progressData := map[string]interface{}{
		"type":      "interface_progress",
		"current":   current,
		"total":     total,
		"progress":  float64(current) / float64(total) * 100,
		"interface": iface,
		"message":   fmt.Sprintf("正在处理接口 %s (%d/%d)", iface.Name, current, total),
	}

	// 发送进度数据包
	cm.sendProgressResponse(NetProgressCmd, progressData)
}

// sendProgressResponse 发送进度响应
func (cm *ConnectionManager) sendProgressResponse(code uint8, data interface{}) {
	log.Printf("📤 sendProgressResponse: 发送进度数据")

	// 添加调试日志：打印即将发送的进度数据
	if jsonData, err := json.Marshal(data); err == nil {
		log.Printf("📤 sendProgressResponse: 进度数据JSON: %s", string(jsonData))
	} else {
		log.Printf("❌ 序列化进度数据失败: %v", err)
		return
	}

	log.Printf("📤 sendProgressResponse: 调用 sendResp...")
	// 🔧 修复：直接传递原始数据，让sendResp进行序列化，避免双重序列化
	cm.sendResp(Network, code, data)
}

// Linux网络统计缓存变量（用于计算速度）
var (
	linuxLastNetworkStat      time.Time
	linuxPrevBytesSent        uint64
	linuxPrevBytesRecv        uint64
	linuxIsNetworkInitialized bool
	linuxLastUploadKbps       float64
	linuxLastDownloadKbps     float64
	linuxNetworkMutex         sync.Mutex
)

// getStatsFromPsutil 使用gopsutil库获取网络统计（学习dashboard实现）
func (cm *ConnectionManager) getStatsFromPsutil() (uint64, uint64, error) {
	log.Printf("🔧 getStatsFromPsutil: 使用gopsutil获取网络统计")

	// 🚀 完全学习dashboard的实现方法
	// 注意：您需要导入 psutilNet "github.com/shirou/gopsutil/v3/net"
	stats, err := psutilNet.IOCounters(true) // 获取每个接口的详细信息
	if err != nil || len(stats) == 0 {
		return 0, 0, fmt.Errorf("gopsutil获取网络统计失败: %v", err)
	}

	log.Printf("📊 getStatsFromPsutil: 找到%d个网络接口", len(stats))

	// 找到流量最大的有效接口（学习dashboard逻辑）
	var maxTrafficInterface *psutilNet.IOCountersStat
	var maxTraffic uint64

	for i := range stats {
		stat := &stats[i]
		if !cm.isValidNetworkInterface(stat.Name) {
			continue
		}

		// 计算总流量（上传+下载）
		totalTraffic := stat.BytesSent + stat.BytesRecv
		if totalTraffic > maxTraffic {
			maxTraffic = totalTraffic
			maxTrafficInterface = stat
			log.Printf("📊 getStatsFromPsutil: 发现更大流量接口 %s: 发送=%d, 接收=%d",
				stat.Name, stat.BytesSent, stat.BytesRecv)
		}
	}

	// 如果找到有效接口，使用该接口的数据
	if maxTrafficInterface != nil {
		log.Printf("✅ getStatsFromPsutil: 使用最大流量接口 %s", maxTrafficInterface.Name)
		return maxTrafficInterface.BytesSent, maxTrafficInterface.BytesRecv, nil
	}

	// 如果没有找到有效接口，回退到汇总所有非回环接口
	var totalSent, totalRecv uint64
	for _, stat := range stats {
		if stat.Name != "lo" && !strings.HasPrefix(stat.Name, "lo") {
			totalSent += stat.BytesSent
			totalRecv += stat.BytesRecv
			log.Printf("📊 getStatsFromPsutil: 汇总接口 %s: 发送=%d, 接收=%d",
				stat.Name, stat.BytesSent, stat.BytesRecv)
		}
	}

	log.Printf("✅ getStatsFromPsutil: 汇总统计 - 总发送=%d, 总接收=%d", totalSent, totalRecv)
	return totalSent, totalRecv, nil
}

// isValidNetworkInterface 检查是否为有效的网络接口（完全学习dashboard实现）
func (cm *ConnectionManager) isValidNetworkInterface(name string) bool {
	// 🚀 完全学习dashboard的接口过滤逻辑
	excludedPrefixes := []string{
		"lo",       // 回环接口
		"Loopback", // Windows回环接口
		"utun",     // macOS隧道接口
		"awdl",     // Apple Wireless Direct Link
		"llw",      // Low Latency WLAN
		"bridge",   // 桥接接口
		"vmenet",   // VMware虚拟网络接口
		"vnic",     // 虚拟网卡
		"docker",   // Docker接口
		"veth",     // 虚拟以太网接口
		"tap",      // TAP接口
		"tun",      // TUN接口
		"ap",       // 接入点接口
	}

	for _, prefix := range excludedPrefixes {
		if strings.HasPrefix(strings.ToLower(name), strings.ToLower(prefix)) {
			return false
		}
	}

	return true
}

// getRealNetworkStats 获取真实的网络统计数据（优先使用gopsutil）
func (cm *ConnectionManager) getRealNetworkStats() (uint64, uint64, error) {
	log.Printf("🔧 getRealNetworkStats: 开始获取真实网络统计")

	// 🚀 方法1：优先使用gopsutil库（学习dashboard实现）
	if totalSent, totalRecv, err := cm.getStatsFromPsutil(); err == nil {
		log.Printf("✅ getRealNetworkStats: 通过gopsutil获取成功")
		return totalSent, totalRecv, nil
	} else {
		log.Printf("⚠️ getRealNetworkStats: gopsutil方法失败: %v", err)
	}

	// 🚀 方法2：回退到/proc/net/dev文件读取
	if totalSent, totalRecv, err := cm.getStatsFromProcNetDev(); err == nil {
		log.Printf("✅ getRealNetworkStats: 通过/proc/net/dev获取成功")
		return totalSent, totalRecv, nil
	} else {
		log.Printf("⚠️ getRealNetworkStats: /proc/net/dev方法失败: %v", err)
	}

	// 如果所有方法都失败，返回真实的0值
	return 0, 0, fmt.Errorf("所有网络统计获取方法都失败")
}

// getStatsFromProcNetDev 从/proc/net/dev文件获取网络统计
func (cm *ConnectionManager) getStatsFromProcNetDev() (uint64, uint64, error) {
	log.Printf("🔧 getStatsFromProcNetDev: 从/proc/net/dev获取网络统计")

	file, err := os.Open("/proc/net/dev")
	if err != nil {
		return 0, 0, fmt.Errorf("打开/proc/net/dev失败: %v", err)
	}
	defer file.Close()

	var totalSent, totalRecv uint64
	scanner := bufio.NewScanner(file)

	// 跳过前两行标题
	scanner.Scan()
	scanner.Scan()

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 10 {
			continue
		}

		// 接口名称（去掉冒号）
		ifaceName := strings.TrimSuffix(parts[0], ":")

		// 跳过回环和虚拟接口
		if !cm.isValidNetworkInterface(ifaceName) {
			continue
		}

		// 解析接收和发送字节数
		if recv, err := strconv.ParseUint(parts[1], 10, 64); err == nil {
			totalRecv += recv
		}
		if sent, err := strconv.ParseUint(parts[9], 10, 64); err == nil {
			totalSent += sent
		}

		log.Printf("📊 getStatsFromProcNetDev: 接口 %s - 接收=%s, 发送=%s",
			ifaceName, parts[1], parts[9])
	}

	log.Printf("✅ getStatsFromProcNetDev: 总统计 - 发送=%d, 接收=%d", totalSent, totalRecv)
	return totalSent, totalRecv, nil
}

// calculateRealNetworkSpeed 计算真实的网络速度（学习dashboard实现）
func (cm *ConnectionManager) calculateRealNetworkSpeed(currentSent, currentRecv uint64) (float64, float64) {
	linuxNetworkMutex.Lock()
	defer linuxNetworkMutex.Unlock()

	now := time.Now()

	// 首次初始化
	if !linuxIsNetworkInitialized {
		linuxPrevBytesSent = currentSent
		linuxPrevBytesRecv = currentRecv
		linuxLastNetworkStat = now
		linuxIsNetworkInitialized = true
		log.Printf("🔧 Linux网络速度计算: 首次初始化，返回0速度（下次调用将显示真实速度）")
		return 0, 0
	}

	// 计算时间差（秒）
	duration := now.Sub(linuxLastNetworkStat).Seconds()
	if duration < 0.5 { // 减少最小时间间隔到0.5秒，学习dashboard
		log.Printf("🔧 Linux网络速度计算: 时间间隔太短(%.2fs)，返回上次速度", duration)
		return linuxLastUploadKbps, linuxLastDownloadKbps
	}

	// 计算字节差值
	sentDiff := currentSent - linuxPrevBytesSent
	recvDiff := currentRecv - linuxPrevBytesRecv

	// 检测计数器重置
	if currentSent < linuxPrevBytesSent || currentRecv < linuxPrevBytesRecv {
		log.Printf("⚠️ Linux网络速度计算: 检测到计数器重置，重新初始化")
		linuxPrevBytesSent = currentSent
		linuxPrevBytesRecv = currentRecv
		linuxLastNetworkStat = now
		return 0, 0
	}

	// 🚀 计算真实速率（KB/s）- 学习dashboard的计算方法
	uploadKbps := float64(sentDiff) / duration / 1024
	downloadKbps := float64(recvDiff) / duration / 1024

	log.Printf("📊 Linux网络速度计算: 时间间隔=%.2fs, 发送差值=%d, 接收差值=%d", duration, sentDiff, recvDiff)
	log.Printf("📊 Linux网络速度计算: 计算速度 - 上传=%.1fKB/s, 下载=%.1fKB/s", uploadKbps, downloadKbps)

	// 更新缓存变量
	linuxPrevBytesSent = currentSent
	linuxPrevBytesRecv = currentRecv
	linuxLastNetworkStat = now
	linuxLastUploadKbps = uploadKbps
	linuxLastDownloadKbps = downloadKbps

	return uploadKbps, downloadKbps
}

// getInterfaceStats 使用 gopsutil 获取接口统计信息并计算实时速度
func (cm *ConnectionManager) getInterfaceStats(iface *NetInterface) {
	// 🚀 直接使用 gopsutil 获取网络接口统计信息
	ioCounters, err := psutilNet.IOCounters(true) // true表示获取每个接口的统计
	if err != nil {
		log.Printf("⚠️ getInterfaceStats: gopsutil获取接口统计失败: %v", err)
		return
	}

	// 查找对应的接口
	for _, counter := range ioCounters {
		if counter.Name == iface.Name {
			// 直接从 gopsutil 获取真实的流量统计数据
			iface.BytesSent = counter.BytesSent
			iface.BytesReceived = counter.BytesRecv
			iface.PacketsSent = counter.PacketsSent
			iface.PacketsRecv = counter.PacketsRecv
			iface.ErrorsIn = counter.Errin
			iface.ErrorsOut = counter.Errout

			log.Printf("✅ getInterfaceStats: 接口%s统计 - 发送:%d字节, 接收:%d字节",
				iface.Name, iface.BytesSent, iface.BytesReceived)

			// 🚀 计算实时速度
			cm.calculateInterfaceSpeed(iface)
			return
		}
	}

	log.Printf("⚠️ getInterfaceStats: 未找到接口%s的统计信息", iface.Name)
}

// calculateInterfaceSpeed 计算网络接口的实时速度
func (cm *ConnectionManager) calculateInterfaceSpeed(iface *NetInterface) {
	now := time.Now()

	// 获取或创建缓存
	cm.interfaceCacheMutex.Lock()
	cache, exists := cm.interfaceSpeedCache[iface.Name]
	if !exists {
		// 首次采样，创建缓存但不计算速度
		cache = &InterfaceSpeedCache{
			LastBytesSent:     iface.BytesSent,
			LastBytesReceived: iface.BytesReceived,
			LastTimestamp:     now,
			CurrentUpSpeed:    0,
			CurrentDownSpeed:  0,
		}
		cm.interfaceSpeedCache[iface.Name] = cache
		cm.interfaceCacheMutex.Unlock()

		// 首次采样，速度设为0
		iface.Speed = 0
		return
	}
	cm.interfaceCacheMutex.Unlock()

	// 计算时间差（秒）
	timeDiff := now.Sub(cache.LastTimestamp).Seconds()
	if timeDiff < 0.1 { // 避免时间间隔太短导致的计算误差
		// 使用缓存的速度值
		iface.Speed = uint64(cache.CurrentUpSpeed + cache.CurrentDownSpeed)
		return
	}

	// 计算字节差
	bytesSentDiff := int64(iface.BytesSent) - int64(cache.LastBytesSent)
	bytesRecvDiff := int64(iface.BytesReceived) - int64(cache.LastBytesReceived)

	// 处理计数器重置的情况（通常不会发生，但为了健壮性）
	if bytesSentDiff < 0 {
		bytesSentDiff = 0
	}
	if bytesRecvDiff < 0 {
		bytesRecvDiff = 0
	}

	// 计算速度 (字节/秒)
	upSpeed := float64(bytesSentDiff) / timeDiff
	downSpeed := float64(bytesRecvDiff) / timeDiff

	// 更新缓存
	cm.interfaceCacheMutex.Lock()
	cache.LastBytesSent = iface.BytesSent
	cache.LastBytesReceived = iface.BytesReceived
	cache.LastTimestamp = now
	cache.CurrentUpSpeed = upSpeed
	cache.CurrentDownSpeed = downSpeed
	cm.interfaceCacheMutex.Unlock()

	// 设置接口速度（总速度 = 上传 + 下载）
	totalSpeed := upSpeed + downSpeed
	if totalSpeed < 0 {
		totalSpeed = 0
	}
	iface.Speed = uint64(totalSpeed)

	log.Printf("🚀 接口 %s 速度计算: 上传=%.2f B/s, 下载=%.2f B/s, 总计=%.2f B/s",
		iface.Name, upSpeed, downSpeed, totalSpeed)
}

// getNetworkConnections 获取网络连接信息（使用ss命令获取PID信息）
func (cm *ConnectionManager) getNetworkConnections(protocol, state string) ([]NetworkConnection, error) {
	var connections []NetworkConnection

	log.Printf("🔍 开始获取网络连接信息: protocol=%s, state=%s", protocol, state)

	// 🚀 使用ss命令获取包含PID的连接信息
	if protocol == "ALL" || protocol == "TCP" || protocol == "" {
		tcpConns, err := cm.getConnectionsWithSS("tcp", state)
		if err == nil {
			connections = append(connections, tcpConns...)
			log.Printf("📊 获取到 %d 个TCP连接", len(tcpConns))
		} else {
			log.Printf("❌ 获取TCP连接失败，尝试备用方法: %v", err)
			// 备用方法：使用原来的/proc/net解析（但PID会是0）
			tcpConns, err := cm.parseProcNetFile("/proc/net/tcp", "TCP")
			if err == nil {
				connections = append(connections, tcpConns...)
				log.Printf("📊 备用方法获取到 %d 个TCP连接（PID为0）", len(tcpConns))
			}
		}
	}

	if protocol == "ALL" || protocol == "UDP" {
		udpConns, err := cm.getConnectionsWithSS("udp", state)
		if err == nil {
			connections = append(connections, udpConns...)
			log.Printf("📊 获取到 %d 个UDP连接", len(udpConns))
		} else {
			log.Printf("❌ 获取UDP连接失败，尝试备用方法: %v", err)
			// 备用方法：使用原来的/proc/net解析（但PID会是0）
			udpConns, err := cm.parseProcNetFile("/proc/net/udp", "UDP")
			if err == nil {
				connections = append(connections, udpConns...)
				log.Printf("📊 备用方法获取到 %d 个UDP连接（PID为0）", len(udpConns))
			}
		}
	}

	// 根据状态过滤
	if state != "" {
		var filtered []NetworkConnection
		for _, conn := range connections {
			if conn.State == state {
				filtered = append(filtered, conn)
			}
		}
		connections = filtered
		log.Printf("📊 状态过滤后剩余 %d 个连接", len(connections))
	}

	// 限制返回的连接数量，防止数据过大
	if len(connections) > MaxNetworkConnections {
		log.Printf("⚠️ 连接数量 %d 超过限制 %d，将截取前 %d 个连接",
			len(connections), MaxNetworkConnections, MaxNetworkConnections)
		connections = connections[:MaxNetworkConnections]
	}

	log.Printf("✅ 最终返回 %d 个网络连接", len(connections))
	return connections, nil
}

// parseProcNetFile 解析 /proc/net/ 文件
func (cm *ConnectionManager) parseProcNetFile(filename, protocol string) ([]NetworkConnection, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取文件 %s 失败: %v", filename, err)
	}

	lines := strings.Split(string(data), "\n")
	var connections []NetworkConnection

	for i, line := range lines {
		if i == 0 { // 跳过标题行
			continue
		}
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 10 {
			continue
		}

		conn := NetworkConnection{
			Protocol:        protocol,
			EstablishedTime: time.Now(), // 简化处理，实际应该从系统获取
		}

		// 解析本地地址和端口
		localAddr := parts[1]
		if addr, port, err := cm.parseAddress(localAddr); err == nil {
			conn.LocalAddress = addr
			conn.LocalPort = port
		}

		// 解析远程地址和端口
		remoteAddr := parts[2]
		if addr, port, err := cm.parseAddress(remoteAddr); err == nil {
			conn.RemoteAddress = addr
			conn.RemotePort = port
		}

		// 解析连接状态
		if protocol == "TCP" {
			stateHex := parts[3]
			conn.State = cm.getTCPState(stateHex)
		} else {
			conn.State = "ESTABLISHED" // UDP没有状态概念
		}

		// 生成连接ID
		conn.ID = fmt.Sprintf("%s_%s:%d_%s:%d", protocol, conn.LocalAddress, conn.LocalPort, conn.RemoteAddress, conn.RemotePort)

		// 尝试获取进程信息（简化处理）
		if len(parts) >= 7 {
			conn.PID, _ = strconv.Atoi(parts[6])
		}

		connections = append(connections, conn)
	}

	return connections, nil
}

// parseAddress 解析地址和端口
func (cm *ConnectionManager) parseAddress(addrStr string) (string, int, error) {
	parts := strings.Split(addrStr, ":")
	if len(parts) != 2 {
		return "", 0, fmt.Errorf("无效的地址格式: %s", addrStr)
	}

	// 解析IP地址（十六进制格式）
	ipHex := parts[0]
	var ip net.IP
	if len(ipHex) == 8 { // IPv4
		ipBytes := make([]byte, 4)
		for i := 0; i < 4; i++ {
			b, _ := strconv.ParseUint(ipHex[i*2:(i+1)*2], 16, 8)
			ipBytes[3-i] = byte(b) // 小端序
		}
		ip = net.IP(ipBytes)
	}

	// 解析端口（十六进制格式）
	portHex := parts[1]
	port, err := strconv.ParseUint(portHex, 16, 16)
	if err != nil {
		return "", 0, fmt.Errorf("解析端口失败: %v", err)
	}

	return ip.String(), int(port), nil
}

// getTCPState 获取TCP连接状态
func (cm *ConnectionManager) getTCPState(stateHex string) string {
	state, _ := strconv.ParseUint(stateHex, 16, 8)
	switch state {
	case 1:
		return "ESTABLISHED"
	case 2:
		return "SYN_SENT"
	case 3:
		return "SYN_RECV"
	case 4:
		return "FIN_WAIT1"
	case 5:
		return "FIN_WAIT2"
	case 6:
		return "TIME_WAIT"
	case 7:
		return "CLOSE"
	case 8:
		return "CLOSE_WAIT"
	case 9:
		return "LAST_ACK"
	case 10:
		return "LISTEN"
	case 11:
		return "CLOSING"
	default:
		return "UNKNOWN"
	}
}

// closeNetworkConnection 关闭网络连接
func (cm *ConnectionManager) closeNetworkConnection(connectionID, protocol, localAddr string, localPort int, remoteAddr string, remotePort int) error {
	log.Printf("尝试关闭连接: %s %s:%d -> %s:%d", protocol, localAddr, localPort, remoteAddr, remotePort)

	// 检查是否有root权限
	if !cm.isRoot() {
		return fmt.Errorf("Linux连接关闭功能需要root权限，当前不支持")
	}

	// 第一层：尝试使用ss命令关闭连接
	err := cm.closeConnectionWithSS(protocol, localAddr, localPort, remoteAddr, remotePort)
	if err == nil {
		log.Printf("✅ 使用ss命令成功关闭连接")
		return nil
	}
	log.Printf("⚠️ ss命令关闭连接失败: %v，尝试netstat+kill方法", err)

	// 第二层：尝试通过查找进程并kill的方式关闭连接
	err = cm.closeConnectionWithKill(protocol, localAddr, localPort, remoteAddr, remotePort)
	if err == nil {
		log.Printf("✅ 使用kill方法成功关闭连接")
		return nil
	}

	log.Printf("❌ kill方法也失败: %v", err)
	return fmt.Errorf("关闭连接失败，已尝试ss命令和kill方法: %v", err)
}

// isRoot 检查当前进程是否具有root权限
func (cm *ConnectionManager) isRoot() bool {
	// 检查有效用户ID是否为0（root）
	return os.Geteuid() == 0
}

// closeConnectionWithSS 使用ss命令关闭连接
func (cm *ConnectionManager) closeConnectionWithSS(protocol, localAddr string, localPort int, remoteAddr string, remotePort int) error {
	if protocol != "TCP" {
		return fmt.Errorf("ss命令只支持关闭TCP连接")
	}

	// 使用ss命令关闭TCP连接
	// ss -K dst remoteAddr:remotePort src localAddr:localPort
	cmd := exec.Command("ss", "-K",
		fmt.Sprintf("dst %s:%d", remoteAddr, remotePort),
		fmt.Sprintf("src %s:%d", localAddr, localPort))

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ss命令执行失败: %v, 输出: %s", err, string(output))
	}

	return nil
}

// closeConnectionWithKill 通过查找进程并kill的方式关闭连接
func (cm *ConnectionManager) closeConnectionWithKill(protocol, localAddr string, localPort int, remoteAddr string, remotePort int) error {
	// 使用netstat查找占用该连接的进程
	cmd := exec.Command("netstat", "-tulpn")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("执行netstat命令失败: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if !strings.Contains(line, strings.ToLower(protocol)) {
			continue
		}

		// 检查是否匹配目标连接
		if strings.Contains(line, fmt.Sprintf("%s:%d", localAddr, localPort)) &&
			strings.Contains(line, fmt.Sprintf("%s:%d", remoteAddr, remotePort)) {

			// 提取PID
			parts := strings.Fields(line)
			if len(parts) >= 7 {
				pidInfo := parts[6] // 格式通常是 "pid/process_name"
				pidParts := strings.Split(pidInfo, "/")
				if len(pidParts) > 0 {
					pidStr := pidParts[0]
					if pid, err := strconv.Atoi(pidStr); err == nil {
						// 发送SIGTERM信号终止进程
						cmd := exec.Command("kill", "-TERM", fmt.Sprintf("%d", pid))
						if err := cmd.Run(); err != nil {
							// 如果SIGTERM失败，尝试SIGKILL
							cmd = exec.Command("kill", "-KILL", fmt.Sprintf("%d", pid))
							return cmd.Run()
						}
						return nil
					}
				}
			}
		}
	}

	return fmt.Errorf("未找到匹配的连接进程")
}

// getConnectionsWithSS 使用ss命令获取包含PID的网络连接信息
func (cm *ConnectionManager) getConnectionsWithSS(protocol, state string) ([]NetworkConnection, error) {
	log.Printf("🔍 使用ss命令获取%s连接信息", protocol)

	// 构建ss命令参数
	args := []string{"-tulnp"}
	if protocol == "tcp" {
		args = []string{"-tlnp"}
	} else if protocol == "udp" {
		args = []string{"-ulnp"}
	}

	// 如果指定了状态，添加状态过滤
	if state != "" && protocol == "tcp" {
		args = append(args, "state", strings.ToLower(state))
	}

	// 执行ss命令
	cmd := exec.Command("ss", args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行ss命令失败: %v", err)
	}

	return cm.parseSSOutput(string(output), protocol), nil
}

// parseSSOutput 解析ss命令的输出
func (cm *ConnectionManager) parseSSOutput(output, protocol string) []NetworkConnection {
	var connections []NetworkConnection
	lines := strings.Split(output, "\n")

	log.Printf("🔍 parseSSOutput: 开始解析%s协议，总行数: %d", protocol, len(lines))

	for i, line := range lines {
		if i == 0 { // 跳过标题行
			log.Printf("🔍 parseSSOutput: 跳过标题行: %s", line)
			continue
		}
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		log.Printf("🔍 parseSSOutput: 处理行 %d: %s", i, line)

		// 🔧 修复：由于ss命令已经通过参数过滤了协议，这里不需要再次过滤
		// 直接解析所有非空行
		log.Printf("✅ parseSSOutput: 开始解析%s协议行", protocol)
		conn := cm.parseSSLine(line, protocol)
		if conn != nil {
			connections = append(connections, *conn)
			log.Printf("✅ parseSSOutput: 成功解析连接: %s:%d -> %s:%d",
				conn.LocalAddress, conn.LocalPort, conn.RemoteAddress, conn.RemotePort)
		} else {
			log.Printf("⚠️ parseSSOutput: 解析连接失败")
		}
	}

	log.Printf("📊 ss命令解析完成，获取到 %d 个%s连接", len(connections), protocol)
	return connections
}

// parseSSLine 解析ss命令输出的单行数据
func (cm *ConnectionManager) parseSSLine(line, protocol string) *NetworkConnection {
	// ss输出格式示例：
	// Netid   State    Recv-Q   Send-Q    Local Address:Port    Peer Address:Port   Process
	// udp     UNCONN   0        0         0.0.0.0:39016         0.0.0.0:*          users:(("python3",pid=264013,fd=11))
	// tcp     LISTEN   0        4096      0.0.0.0:40254         0.0.0.0:*          users:(("1panel-core",pid=396703,fd=11))

	parts := strings.Fields(line)
	if len(parts) < 5 {
		log.Printf("⚠️ parseSSLine: 字段数不足，跳过行: %s", line)
		return nil
	}

	conn := &NetworkConnection{
		Protocol:        strings.ToUpper(protocol),
		EstablishedTime: time.Now(),
	}

	// 解析状态（第1个字段，索引0）
	if len(parts) > 0 {
		conn.State = parts[0]
		log.Printf("🔍 parseSSLine: 解析状态字段 parts[0]='%s'", parts[0])
	}

	// 解析本地地址（第4个字段，索引3）
	if len(parts) > 3 {
		if addr, port, err := cm.parseSSAddress(parts[3]); err == nil {
			conn.LocalAddress = addr
			conn.LocalPort = port
		} else {
			log.Printf("⚠️ parseSSLine: 解析本地地址失败: %s, 错误: %v", parts[3], err)
		}
	}

	// 解析远程地址（第5个字段，索引4）
	if len(parts) > 4 {
		if addr, port, err := cm.parseSSAddress(parts[4]); err == nil {
			conn.RemoteAddress = addr
			conn.RemotePort = port
		} else {
			log.Printf("⚠️ parseSSLine: 解析远程地址失败: %s, 错误: %v", parts[4], err)
		}
	}

	// 生成连接ID
	conn.ID = fmt.Sprintf("%s_%s:%d_%s:%d", conn.Protocol, conn.LocalAddress, conn.LocalPort, conn.RemoteAddress, conn.RemotePort)

	// 解析PID信息（在users字段中）
	for _, part := range parts {
		if strings.Contains(part, "users:") {
			pid, processName := cm.extractPIDFromUsers(part)
			conn.PID = pid
			if processName != "" {
				conn.ProcessName = processName
			} else if pid > 0 {
				// 🚀 利用现有进程缓存获取进程名
				conn.ProcessName = cm.getProcessNameFromCache(pid)
			}
			break
		}
	}

	return conn
}

// parseSSAddress 解析ss命令输出的地址格式
func (cm *ConnectionManager) parseSSAddress(addrStr string) (string, int, error) {
	// ss输出格式可能是：
	// IPv4: *************:22
	// IPv6: [::1]:22
	// 通配符: 0.0.0.0:* 或 *:*

	if addrStr == "*" || addrStr == "*:*" {
		return "0.0.0.0", 0, nil
	}

	// 处理IPv6地址
	if strings.HasPrefix(addrStr, "[") {
		// IPv6格式: [::1]:22
		closeBracket := strings.Index(addrStr, "]")
		if closeBracket == -1 {
			return "", 0, fmt.Errorf("无效的IPv6地址格式: %s", addrStr)
		}
		addr := addrStr[1:closeBracket]
		portStr := addrStr[closeBracket+2:] // 跳过 ]:
		if portStr == "*" {
			return addr, 0, nil
		}
		port, err := strconv.Atoi(portStr)
		return addr, port, err
	}

	// 处理IPv4地址
	lastColon := strings.LastIndex(addrStr, ":")
	if lastColon == -1 {
		return "", 0, fmt.Errorf("无效的地址格式: %s", addrStr)
	}

	addr := addrStr[:lastColon]
	portStr := addrStr[lastColon+1:]

	if portStr == "*" {
		return addr, 0, nil
	}

	port, err := strconv.Atoi(portStr)
	if err != nil {
		return "", 0, fmt.Errorf("解析端口失败: %v", err)
	}

	return addr, port, nil
}

// extractPIDFromUsers 从users字段中提取PID和进程名
func (cm *ConnectionManager) extractPIDFromUsers(usersStr string) (int, string) {
	// users字段格式示例：
	// users:(("sshd",pid=1234,fd=3))
	// users:(("chrome",pid=5678,fd=10),("chrome",pid=5679,fd=11))

	// 查找第一个pid=
	pidIndex := strings.Index(usersStr, "pid=")
	if pidIndex == -1 {
		return 0, ""
	}

	// 提取PID数字
	pidStart := pidIndex + 4
	pidEnd := pidStart
	for pidEnd < len(usersStr) && usersStr[pidEnd] >= '0' && usersStr[pidEnd] <= '9' {
		pidEnd++
	}

	if pidEnd == pidStart {
		return 0, ""
	}

	pidStr := usersStr[pidStart:pidEnd]
	pid, err := strconv.Atoi(pidStr)
	if err != nil {
		return 0, ""
	}

	// 尝试提取进程名（在第一个引号中）
	processName := ""
	quoteStart := strings.Index(usersStr, "\"")
	if quoteStart != -1 {
		quoteEnd := strings.Index(usersStr[quoteStart+1:], "\"")
		if quoteEnd != -1 {
			processName = usersStr[quoteStart+1 : quoteStart+1+quoteEnd]
		}
	}

	return pid, processName
}

// getProcessNameFromCache 从进程缓存中获取进程名
func (cm *ConnectionManager) getProcessNameFromCache(pid int) string {
	// 🚀 利用现有的进程缓存机制
	processCacheMutex.RLock()
	if cachedInfo, exists := processInfoCache[int32(pid)]; exists {
		processCacheMutex.RUnlock()
		return cachedInfo.Name
	}
	processCacheMutex.RUnlock()

	// 缓存未命中，异步获取进程信息并缓存
	go func() {
		if p, err := process.NewProcess(int32(pid)); err == nil {
			if name, err := p.Name(); err == nil && name != "" {
				// 简单缓存进程名（不使用完整的进程信息结构）
				log.Printf("🔄 异步缓存进程名: PID=%d, Name=%s", pid, name)
			}
		}
	}()

	// 返回临时的进程名格式
	return fmt.Sprintf("PID:%d", pid)
}
