package initializer

import (
	"time"
	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"server/core/dbpool"
	"server/core/internal"
	"server/global"
)

func initDB() *gorm.DB {
	s := global.CONFIG.Sqlite
	DB, err := gorm.Open(sqlite.Open(s.Dsn()), internal.Gorm.Config(s.Prefix, s.Singular))
	if err != nil {
		zap.L().Fatal("打开sqlite数据库失败: ", zap.Error(err))
	}

	// 🚨 修复阻塞问题：配置数据库连接池参数
	sqlDB, err := DB.DB()
	if err != nil {
		zap.L().Fatal("获取底层数据库连接失败: ", zap.Error(err))
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(s.MaxIdleConns)    // 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxOpenConns(s.MaxOpenConns)    // 设置打开数据库连接的最大数量
	sqlDB.SetConnMaxLifetime(time.Hour)      // 设置了连接可复用的最大时间
	sqlDB.SetConnMaxIdleTime(10 * time.Minute) // 设置连接空闲超时时间

	zap.L().Info("连接sqlite成功，已配置连接池参数",
		zap.Int("maxIdleConns", s.MaxIdleConns),
		zap.Int("maxOpenConns", s.MaxOpenConns))

	// 🚀 初始化数据库连接池管理器
	if err := dbpool.InitGlobalDBPoolManager(DB); err != nil {
		zap.L().Fatal("初始化数据库连接池管理器失败", zap.Error(err))
	}

	// 🚀 启动数据库连接池升级监控
	dbpool.StartUpgradeMonitoring()

	return DB
}
