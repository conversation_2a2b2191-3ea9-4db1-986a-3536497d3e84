package initializer

import (
	"time"
	"server/core/cache"
	"server/core/events"
	"server/core/internal"
	"server/core/listener/pipe"
	"server/core/listener/tcp"
	"server/core/manager"
	"server/core/workerpool"
	"server/global"
	"server/service/c2"
	"server/utils"

	"go.uber.org/zap"
)

func InitSystem() {
	// 记录服务器启动时间
	global.StartTime = time.Now()

	global.VP = initViper()
	global.LOG = initZap()
	zap.ReplaceGlobals(global.LOG)

	// 自动配置辅助，检测并设置磁盘列表和JWT签名密钥
	utils.AutoConfigHelper()

	global.DB = initDB()
	if global.DB != nil {
		RegistTable()
	}
	OtherInit()
	internal.SysUserInit()

	// 🚀 初始化智能缓存系统
	cache.InitGlobalSmartCache()

	// 初始化工作池管理器
	workerpool.GlobalPoolManager.InitDefaultPools()

	// 初始化Goroutine泄漏检测器
	workerpool.InitGlobalLeakDetector()

	// 初始化代理管理器（在数据库初始化后）
	manager.InitProxyManagers()

	// 初始化SSE管理器
	manager.InitSSEManager()

	// 初始化客户端事件管理器
	events.InitClientEventManager()

	// 初始化客户端事件处理器
	clientService := &c2.ClientService{}
	clientService.InitClientEventHandlers()

	// 初始化默认心跳配置
	heartbeatConfigService := &c2.HeartbeatConfigService{}
	if err := heartbeatConfigService.InitDefaultGlobalConfig(); err != nil {
		global.LOG.Error("初始化默认心跳配置失败", zap.Error(err))
	} else {
		global.LOG.Info("初始化默认心跳配置成功")
	}

	// 初始化pipe监听器
	pipe.InitPipeListeners()
	// 初始化tcp监听器
	tcp.InitTCPListeners()
}
