package initializer

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"server/core/dbpool"
	"server/core/manager"
	"server/core/shutdown"
	"server/core/static"
	"server/global"
	"server/middleware"
	"server/model/sys"
	"server/route"
	"server/service/c2"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SetAllClientsOffline 将所有在线客户端设置为离线状态
func SetAllClientsOffline() (count int64, err error) {
	// 🚀 使用数据库连接池批量更新所有在线客户端状态为离线
	err = dbpool.ExecuteDBOperationAsyncAndWait("clients_offline_batch", func(db *gorm.DB) error {
		result := db.Model(&sys.Client{}).Where("status = ?", 1).Updates(map[string]interface{}{
			"status":         0,
			"last_active_at": time.Now(),
		})

		if result.Error != nil {
			return result.Error
		}

		count = result.RowsAffected
		return nil
	})

	if err != nil {
		return 0, err
	}

	global.LOG.Info("服务器关闭时将所有在线客户端设置为离线", zap.Int64("affected_count", count))
	return count, nil
}

func InitServer(r *gin.Engine) {
	// 启动离线检测服务
	heartbeatService := &c2.HeartbeatService{}
	heartbeatService.StartOfflineDetection()

	address := fmt.Sprintf(":%d", global.CONFIG.Server.PORT)
	srv := &http.Server{
		Addr:    address,
		Handler: r,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			zap.L().Error("server启动失败", zap.Error(err))
			os.Exit(1)
		}
	}()

	quit := make(chan os.Signal, 1)

	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	zap.L().Info("关闭主Server")

	// 🚀 触发全局关闭信号，通知所有SSE和长连接
	shutdown.Shutdown()

	// 停止SSE管理器
	if manager.GlobalSSEManager != nil {
		manager.GlobalSSEManager.Stop()
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second) // 增加超时时间
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		zap.L().Fatal("WEB服务关闭异常", zap.Error(err))
	}

	zap.L().Info("WEB服务已关闭")

	// 在关闭服务器前，将所有在线客户端设置为离线
	if count, err := SetAllClientsOffline(); err != nil {
		zap.L().Error("设置客户端离线状态失败", zap.Error(err))
	} else {
		zap.L().Info("成功将所有在线客户端设置为离线", zap.Int64("count", count))
	}

	if global.DB != nil {
		sqlDB, err := global.DB.DB()
		if err != nil {
			zap.L().Error("获取数据库实例失败", zap.Error(err))
		} else {
			if err := sqlDB.Close(); err != nil {
				zap.L().Error("关闭数据库连接失败", zap.Error(err))
			} else {
				zap.L().Info("数据库连接已关闭")
			}
		}
	}
	if err := CleanClientBinFiles(); err != nil {
		zap.L().Error("清理客户端二进制文件失败", zap.Error(err))
	}
}

func InitMainRoute() *gin.Engine {
	Router := gin.New()
	Router.Use(gin.Recovery())
	Router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, HEAD")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-Token")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Range")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")

		// 处理OPTIONS请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
	})

	if gin.Mode() == gin.DebugMode {
		Router.Use(gin.Logger())
	}

	// 注册API路由
	sys := route.RouteGroupManagerAPP.Sys
	C2 := route.RouteGroupManagerAPP.C2
	publicRoute := Router.Group(global.CONFIG.Server.RouterPrefix)
	privateRoute := Router.Group(global.CONFIG.Server.RouterPrefix)
	privateRoute.Use(middleware.JWTAuth()) //添加JWT验证
	{
		sys.InitAuthRoute(publicRoute)
		sys.InitConfigRoute(publicRoute) // 添加配置路由，不需要认证
		C2.InitClientWSRoute(publicRoute)
		// WebSocket连接路由，不需要JWT中间件验证，在HandleWebSocket函数中自行验证token，同时由于HTTP升级为Websocket无法继承JWT，所以放这
	}

	{
		sys.InitDashboardRoute(privateRoute)
		C2.InitListenerRoute(privateRoute)
		sys.InitUpdateUserInfoRoute(privateRoute)
		sys.InitUserManageRoute(privateRoute)         // 添加用户管理路由
		sys.InitLogRoute(privateRoute)                // 添加日志监控路由
		sys.InitFileTransferStatsRouter(privateRoute) // 添加文件传输统计路由
		sys.InitPerformanceRouter(privateRoute)       // 添加性能监控路由
		sys.InitNotificationRoute(privateRoute)       // 添加通知路由
		C2.InitClientRoute(privateRoute)
		C2.InitFileRoute(privateRoute)
		C2.InitDirRoute(privateRoute)
		C2.InitProcRoute(privateRoute)
		C2.InitScreenshotRoute(privateRoute)
		C2.InitNetworkRoute(privateRoute)         // 添加网络监控路由
		C2.InitHeartbeatConfigRoute(privateRoute) // 添加心跳配置路由
		C2.InitProxyRoute(privateRoute)
		sys.InitDownloadRouter(privateRoute)
		sys.InitUploadRouter(privateRoute)
	}

	// 注册静态文件服务
	// 在开发环境中，前端通过 npm run dev 提供服务，不需要后端提供静态文件
	// 在生产环境中，使用嵌入的静态文件或本地 dist 目录
	if gin.Mode() != gin.DebugMode {
		// 使用项目根目录下的 dist 文件夹作为本地静态文件目录
		// 如果不存在，则使用嵌入的静态文件
		static.ServeStatic(Router, "./dist")
	}

	return Router
}

// CleanClientBinFiles 清理客户端二进制文件目录
func CleanClientBinFiles() error {

	// 确保CLIENT_BIN_DIR已初始化
	if global.CLIENT_BIN_DIR == "" {
		return fmt.Errorf("客户端二进制文件目录未初始化")
	}

	// 删除指定的客户端文件
	files, err := os.ReadDir(global.CLIENT_BIN_DIR)
	if err != nil {
		return fmt.Errorf("读取客户端二进制文件目录失败: %s", err.Error())
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filePath := filepath.Join(global.CLIENT_BIN_DIR, file.Name())
		if err := os.Remove(filePath); err != nil {
			if !os.IsNotExist(err) {
				global.LOG.Error(fmt.Sprintf("删除客户端文件失败: %s, 错误: %s", filePath, err.Error()))
			}
		} else {
			global.LOG.Info(fmt.Sprintf("成功删除客户端文件: %s", filePath))
		}
	}
	return nil
}
