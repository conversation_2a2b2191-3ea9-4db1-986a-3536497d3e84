package manager

import (
	"errors"
	"fmt"
	"math/rand"
	"net"
	"server/core/dbpool"
	"server/core/proxyserver/forward"
	"server/core/proxyserver/socks5"
	"server/global"
	"server/model/basic"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	randomAlloc     = "random"
	sequentialAlloc = "sequential"
)

type ProxyManager struct {
	Proxies map[string]*basic.Proxy //proxy的ID为key
	*PortManager
	lock sync.RWMutex

	// 同步相关
	syncTicker   *time.Ticker
	syncStop     chan struct{}
	syncInterval time.Duration
}

type PortRange struct {
	Start uint16
	End   uint16
}

// ServerPortManager 用于管理 Server 端监听的代理端口
type ServerPortManager struct {
	PortRange PortRange

	usedPorts         map[uint16]bool // 已使用的端口（server 端监听的端口）
	lastAllocatedPort uint16          // 上次分配的端口（用于顺序分配）
}

// ClientPortManager 用于管理为每个 Client 分配的目标端口
type ClientPortManager struct {
	defaultPortRange PortRange          // 默认 Client 端口范围
	customPortRanges map[uint]PortRange // 每个 client 的自定义端口范围（clientID -> range）

	usedPorts         map[uint16]uint   // 端口 -> clientID 的映射
	allocatedClients  map[uint][]uint16 // clientID -> 分配的端口列表
	lastAllocatedPort uint16            // 上次分配的端口（用于顺序分配）
}

// PortManager 是总端口管理器，包含 Server 和 Client 的端口管理
type PortManager struct {
	ServerPortManager *ServerPortManager
	ClientPortManager *ClientPortManager

	sync.Mutex // 用于并发控制
}

func NewPortManager() *PortManager {
	// Server 端口范围配置
	serverStart := global.CONFIG.Server.ServerStartPort
	serverEnd := global.CONFIG.Server.ServerEndPort
	if serverStart == 0 {
		serverStart = 20000
	}
	if serverEnd == 0 {
		serverEnd = 65535
	}

	// Client 端口范围默认与 Server 相同，也可独立配置
	clientStart := global.CONFIG.Server.ServerStartPort
	clientEnd := global.CONFIG.Server.ServerEndPort
	if clientStart == 0 {
		clientStart = serverStart
	}
	if clientEnd == 0 {
		clientEnd = serverEnd
	}

	// 初始化 ServerPortManager
	serverPortManager := &ServerPortManager{
		PortRange:         PortRange{Start: serverStart, End: serverEnd},
		usedPorts:         make(map[uint16]bool),
		lastAllocatedPort: serverStart,
	}
	// 🚀 从数据库加载已使用的 server 端口（如果数据库已初始化）
	var proxies []basic.Proxy
	dbpool.ExecuteDBOperationAsyncAndWait("proxy_ports_load", func(db *gorm.DB) error {
		if err := db.Model(&basic.Proxy{}).Find(&proxies).Error; err == nil {
			for _, p := range proxies {
				if p.UserPort > 0 {
					serverPortManager.usedPorts[p.UserPort] = true
				}
			}
		}
		return nil
	})
	// 初始化 ClientPortManager
	clientPortManager := &ClientPortManager{
		defaultPortRange:  PortRange{Start: clientStart, End: clientEnd},
		customPortRanges:  make(map[uint]PortRange),
		usedPorts:         make(map[uint16]uint),
		allocatedClients:  make(map[uint][]uint16),
		lastAllocatedPort: clientStart,
	}

	return &PortManager{
		ServerPortManager: serverPortManager,
		ClientPortManager: clientPortManager,
	}
}

func NewProxyManager(manager *PortManager) *ProxyManager {
	pm := &ProxyManager{
		Proxies:      make(map[string]*basic.Proxy),
		PortManager:  manager,
		syncInterval: 30 * time.Second, // 默认30秒同步一次
		syncStop:     make(chan struct{}),
	}

	// 🚀 从数据库恢复代理信息
	var proxies []basic.Proxy
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_manager_init", func(db *gorm.DB) error {
		return db.Model(&basic.Proxy{}).Find(&proxies).Error
	}); err != nil {
		global.LOG.Error("初始化ProxyManager出错", zap.Error(err))
	} else {
		for _, proxy := range proxies {
			// 重置内存标记
			proxy.MemoryDirty = false
			proxy.LastSyncTime = time.Now()
			pm.Proxies[proxy.ProxyID] = &proxy
		}
		global.LOG.Info("从数据库恢复代理信息", zap.Int("count", len(proxies)))
	}

	// 启动定期同步
	pm.startPeriodicSync()

	return pm
}

func (pm *ProxyManager) GetProxy(proxyID string) (*basic.Proxy, bool) {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	proxy, exist := pm.Proxies[proxyID]
	return proxy, exist
}

// AddProxy 添加代理（内存+数据库）
func (pm *ProxyManager) AddProxy(proxy *basic.Proxy) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	// 设置版本和同步信息
	if proxy.Version == 0 {
		proxy.Version = 1
	}
	proxy.MemoryDirty = true
	proxy.LastSyncTime = time.Now()

	// 添加到内存
	pm.Proxies[proxy.ProxyID] = proxy

	// 🚀 异步同步到数据库
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_add", func(db *gorm.DB) error {
		return db.Save(proxy).Error
	}); err != nil {
		// 如果数据库保存失败，从内存中移除
		delete(pm.Proxies, proxy.ProxyID)
		return err
	}

	proxy.MemoryDirty = false
	global.LOG.Info("添加代理成功", zap.String("proxyID", proxy.ProxyID))
	return nil
}

// RemoveProxy 移除代理（内存+数据库）
func (pm *ProxyManager) RemoveProxy(proxyID string) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	proxy, exists := pm.Proxies[proxyID]
	if !exists {
		return fmt.Errorf("代理不存在: %s", proxyID)
	}

	// 🚀 从数据库删除
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete", func(db *gorm.DB) error {
		return db.Delete(proxy).Error
	}); err != nil {
		return err
	}

	// 从内存删除
	delete(pm.Proxies, proxyID)

	global.LOG.Info("移除代理成功", zap.String("proxyID", proxyID))
	return nil
}

// UpdateProxy 更新代理（内存+数据库）
func (pm *ProxyManager) UpdateProxy(newProxy *basic.Proxy) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	oldProxy, exists := pm.Proxies[newProxy.ProxyID]
	if !exists {
		return fmt.Errorf("代理不存在: %s", newProxy.ProxyID)
	}

	// 增加版本号
	newProxy.Version = oldProxy.Version + 1
	newProxy.MemoryDirty = true
	newProxy.LastSyncTime = time.Now()

	// 更新内存
	pm.Proxies[newProxy.ProxyID] = newProxy

	// 🚀 同步到数据库
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_update", func(db *gorm.DB) error {
		return db.Save(newProxy).Error
	}); err != nil {
		// 如果数据库保存失败，恢复旧数据
		pm.Proxies[newProxy.ProxyID] = oldProxy
		return err
	}

	newProxy.MemoryDirty = false
	global.LOG.Debug("更新代理成功", zap.String("proxyID", newProxy.ProxyID))
	return nil
}

var (
	GlobalPortManager  *PortManager
	GlobalProxyManager *ProxyManager
	initOnce           sync.Once
)

// InitProxyManagers 初始化代理管理器（在数据库初始化后调用）
func InitProxyManagers() {
	initOnce.Do(func() {
		GlobalPortManager = NewPortManager()
		GlobalProxyManager = NewProxyManager(GlobalPortManager)
	})
}

// GetGlobalPortManager 安全获取全局端口管理器
func GetGlobalPortManager() *PortManager {
	if GlobalPortManager == nil {
		InitProxyManagers()
	}
	return GlobalPortManager
}

// GetGlobalProxyManager 安全获取全局代理管理器
func GetGlobalProxyManager() *ProxyManager {
	if GlobalProxyManager == nil {
		InitProxyManagers()
	}
	return GlobalProxyManager
}

// ===== 同步相关方法 =====

// startPeriodicSync 启动定期同步
func (pm *ProxyManager) startPeriodicSync() {
	pm.syncTicker = time.NewTicker(pm.syncInterval)
	go func() {
		for {
			select {
			case <-pm.syncTicker.C:
				pm.syncToDatabase()
			case <-pm.syncStop:
				pm.syncTicker.Stop()
				return
			}
		}
	}()
}

// stopPeriodicSync 停止定期同步
func (pm *ProxyManager) stopPeriodicSync() {
	if pm.syncStop != nil {
		close(pm.syncStop)
	}
}

// syncToDatabase 同步内存数据到数据库
func (pm *ProxyManager) syncToDatabase() {
	pm.lock.RLock()
	var dirtyProxies []*basic.Proxy
	for _, proxy := range pm.Proxies {
		if proxy.MemoryDirty {
			dirtyProxies = append(dirtyProxies, proxy)
		}
	}
	pm.lock.RUnlock()

	if len(dirtyProxies) == 0 {
		return
	}

	// 🚀 批量更新数据库
	for _, proxy := range dirtyProxies {
		if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_sync", func(db *gorm.DB) error {
			return db.Save(proxy).Error
		}); err != nil {
			global.LOG.Error("同步代理到数据库失败",
				zap.String("proxyID", proxy.ProxyID),
				zap.Error(err))
		} else {
			pm.lock.Lock()
			proxy.MemoryDirty = false
			proxy.LastSyncTime = time.Now()
			pm.lock.Unlock()
		}
	}

	global.LOG.Debug("同步代理到数据库完成", zap.Int("count", len(dirtyProxies)))
}

// forceSyncToDatabase 强制同步所有数据到数据库
func (pm *ProxyManager) forceSyncToDatabase() {
	pm.lock.RLock()
	proxies := make([]*basic.Proxy, 0, len(pm.Proxies))
	for _, proxy := range pm.Proxies {
		proxies = append(proxies, proxy)
	}
	pm.lock.RUnlock()

	for _, proxy := range proxies {
		// 🚀 使用数据库连接池异步同步代理到数据库
		if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_force_sync", func(db *gorm.DB) error {
			return db.Save(proxy).Error
		}); err != nil {
			global.LOG.Error("强制同步代理到数据库失败",
				zap.String("proxyID", proxy.ProxyID),
				zap.Error(err))
		} else {
			pm.lock.Lock()
			proxy.MemoryDirty = false
			proxy.LastSyncTime = time.Now()
			pm.lock.Unlock()
		}
	}

	global.LOG.Info("强制同步所有代理到数据库完成", zap.Int("count", len(proxies)))
}

func (pm *PortManager) AllocServerPort(mode string) (uint16, error) {
	pm.Lock()
	defer pm.Unlock()

	return pm.ServerPortManager.AllocPort(mode)
}

// StartProxy 统一的代理启动接口
func (pm *ProxyManager) StartProxy(proxy *basic.Proxy) error {
	switch proxy.Type {
	case "forward": // 正向代理
		return pm.StartForwardProxy(proxy)
	case "reverse": // 反向代理
		return pm.StartReverseProxy(proxy)
	default:
		return fmt.Errorf("unsupported proxy type: %s", proxy.Type)
	}
}

// RestartProxy 重启代理
func (pm *ProxyManager) RestartProxy(proxy *basic.Proxy) error {
	if err := pm.StopProxy(proxy); err != nil {
		global.LOG.Warn("停止代理时出错", zap.Error(err))
	}
	return pm.StartProxy(proxy)
}

// GetProxyStats 获取代理统计信息
func (pm *ProxyManager) GetProxyStats() map[string]interface{} {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	stats := map[string]interface{}{
		"total":   len(pm.Proxies),
		"running": 0,
		"stopped": 0,
		"error":   0,
		"forward": 0,
		"reverse": 0,
	}

	for _, proxy := range pm.Proxies {
		switch proxy.Status {
		case 0:
			stats["stopped"] = stats["stopped"].(int) + 1
		case 1:
			stats["running"] = stats["running"].(int) + 1
		case 2:
			stats["error"] = stats["error"].(int) + 1
		}

		switch proxy.Type {
		case "forward":
			stats["forward"] = stats["forward"].(int) + 1
		case "reverse":
			stats["reverse"] = stats["reverse"].(int) + 1
		}
	}

	return stats
}

func (pm *ProxyManager) StartForwardProxy(proxy *basic.Proxy) error {
	// 正向代理不需要在这里分配userPort，应该已经分配好了
	// 直接启动中继服务
	if err := forward.GlobalForwardManager.StartServer(proxy); err != nil {
		return err
	}

	return nil
}

func (pm *ProxyManager) StopForwardProxy(proxy *basic.Proxy) error {
	if err := forward.GlobalForwardManager.StopServer(proxy); err != nil {
		return err
	}
	return nil
}

func (pm *ProxyManager) StartReverseProxy(proxy *basic.Proxy) error {
	if err := socks5.GlobalSocks5Manager.StartServer(proxy); err != nil {
		return err
	}
	return nil
}

func (pm *ProxyManager) StopReverseProxy(proxy *basic.Proxy) error {
	if err := socks5.GlobalSocks5Manager.StopServer(proxy); err != nil {
		return err
	}
	return nil
}

func (pm *ProxyManager) StopProxy(proxy *basic.Proxy) error {
	switch proxy.Type {
	case "forward": // 正向代理
		// 停止正向代理服务
		if err := pm.StopForwardProxy(proxy); err != nil {
			global.LOG.Error("停止正向代理失败", zap.Error(err))
			return err
		}
		// 回收服务器端口
		if proxy.UserPort > 0 {
			pm.PortManager.ReleaseServerPort(proxy.UserPort)
		}
		// 回收客户端端口
		if proxy.ClientPort > 0 {
			pm.PortManager.ReleaseClientPort(proxy.ClientID, proxy.ClientPort)
		}

	case "reverse": // 反向代理
		// 停止反向代理服务
		if err := pm.StopReverseProxy(proxy); err != nil {
			global.LOG.Error("停止反向代理失败", zap.Error(err))
			return err
		}
		// 回收服务器端口（UserPort）
		if proxy.UserPort > 0 {
			pm.PortManager.ReleaseServerPort(proxy.UserPort)
		}
		// 回收客户端端口（ClientPort）
		if proxy.ClientPort > 0 {
			pm.PortManager.ReleaseClientPort(proxy.ClientID, proxy.ClientPort)
		}

	default:
		return fmt.Errorf("unsupported proxy type: %s", proxy.Type)
	}

	// 更新代理状态（通过UpdateProxy自动同步）
	proxy.Status = 0
	if err := pm.UpdateProxy(proxy); err != nil {
		global.LOG.Error("更新代理状态失败", zap.Error(err))
		return err
	}

	return nil
}

// GetProxyByUserPort 根据User端口获取代理
func (pm *ProxyManager) GetProxyByUserPort(port uint16) *basic.Proxy {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	for _, proxy := range pm.Proxies {
		if proxy.UserPort == port {
			return proxy
		}
	}
	return nil
}

// GetProxiesByClient 获取指定客户端的所有代理
func (pm *ProxyManager) GetProxiesByClient(clientID uint) []*basic.Proxy {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	var proxies []*basic.Proxy
	for _, proxy := range pm.Proxies {
		if proxy.ClientID == clientID {
			proxies = append(proxies, proxy)
		}
	}
	return proxies
}

// GetProxiesByStatus 获取指定状态的代理
func (pm *ProxyManager) GetProxiesByStatus(status int) []*basic.Proxy {
	pm.lock.RLock()
	defer pm.lock.RUnlock()
	var proxies []*basic.Proxy
	for _, proxy := range pm.Proxies {
		if proxy.Status == status {
			proxies = append(proxies, proxy)
		}
	}
	return proxies
}

// GetAllProxies 获取所有代理
func (pm *ProxyManager) GetAllProxies() []*basic.Proxy {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	var proxies []*basic.Proxy
	for _, proxy := range pm.Proxies {
		proxies = append(proxies, proxy)
	}
	return proxies
}

// ProxyExists 检查代理是否存在
func (pm *ProxyManager) ProxyExists(proxyID string) bool {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	_, exists := pm.Proxies[proxyID]
	return exists
}

// UpdateProxyStatus 更新代理状态
func (pm *ProxyManager) UpdateProxyStatus(proxyID string, status int) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	if proxy, exists := pm.Proxies[proxyID]; exists {
		proxy.Status = status
		proxy.Version++
		proxy.MemoryDirty = true
		proxy.LastSyncTime = time.Now()
		// 🚀 使用数据库连接池更新状态
		return dbpool.ExecuteDBOperationAsyncAndWait("proxy_status_update", func(db *gorm.DB) error {
			return db.Model(proxy).Update("status", status).Error
		})
	}
	return fmt.Errorf("proxy %s not found", proxyID)
}

// CleanupStoppedProxies 清理已停止的代理
func (pm *ProxyManager) CleanupStoppedProxies() error {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	var toDelete []string
	for id, proxy := range pm.Proxies {
		if proxy.Status == 0 { // 已停止
			toDelete = append(toDelete, id)
		}
	}

	for _, id := range toDelete {
		delete(pm.Proxies, id)
		// 🚀 使用数据库连接池异步删除代理记录
		if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_cleanup_delete", func(db *gorm.DB) error {
			return db.Delete(&basic.Proxy{}, id).Error
		}); err != nil {
			global.LOG.Error("删除代理记录失败", zap.String("proxyID", id), zap.Error(err))
		}
	}

	return nil
}

// SyncWithDatabase 与数据库同步
func (pm *ProxyManager) SyncWithDatabase() error {
	var proxies []basic.Proxy
	// 🚀 使用数据库连接池同步代理数据
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_sync_all", func(db *gorm.DB) error {
		return db.Model(&basic.Proxy{}).Find(&proxies).Error
	}); err != nil {
		return err
	}

	pm.lock.Lock()
	defer pm.lock.Unlock()

	// 清空内存中的代理
	pm.Proxies = make(map[string]*basic.Proxy)

	// 重新加载
	for _, proxy := range proxies {
		pm.Proxies[proxy.ProxyID] = &proxy
	}

	return nil
}

// ValidateProxy 验证代理配置
func (pm *ProxyManager) ValidateProxy(proxy *basic.Proxy) error {
	if proxy.ClientID == 0 {
		return errors.New("client ID cannot be zero")
	}

	if proxy.Type != "forward" && proxy.Type != "reverse" {
		return fmt.Errorf("invalid proxy type: %s", proxy.Type)
	}

	if proxy.UserPort != 0 {
		if ok, _ := CheckPortAvailable(proxy.UserPort); !ok {
			return fmt.Errorf("port %d is not available", proxy.UserPort)
		}
	}
	return nil
}

// AllocPort 方法
func (spm *ServerPortManager) AllocPort(mode string) (uint16, error) {
	start := spm.PortRange.Start
	end := spm.PortRange.End

	if start == 0 || end == 0 || start > end {
		return 0, fmt.Errorf("invalid server port range: %d - %d", start, end)
	}

	var port uint16
	var found bool

	switch mode {
	case "random":
		for i := 0; i <= int(end-start); i++ {
			port = uint16(rand.Intn(int(end-start)+1)) + start
			if !spm.usedPorts[port] {
				if ok, _ := CheckPortAvailable(port); ok {
					found = true
					break
				}
			}
		}
	case "sequential":
		current := spm.lastAllocatedPort
		if current < start || current > end {
			current = start
		}
		for i := 0; i <= int(end-start); i++ {
			port = ((current-start)+1+uint16(i))%(end-start+1) + start
			if !spm.usedPorts[port] {
				if ok, _ := CheckPortAvailable(port); ok {
					spm.lastAllocatedPort = port
					found = true
					break
				}
			}
		}
	default:
		return 0, fmt.Errorf("unknown allocation mode: %s", mode)
	}

	if found {
		spm.usedPorts[port] = true
		return port, nil
	}

	return 0, fmt.Errorf("no available server port in range %d - %d", start, end)
}

// CheckPortAvailable 检查指定端口是否可用
func CheckPortAvailable(port uint16) (bool, string) {
	tcpAddr := fmt.Sprintf(":%d", port)
	tcpListener, err := net.Listen("tcp", tcpAddr)
	if err != nil {
		return false, fmt.Sprintf("TCP端口被占用: %v", err)
	}
	tcpListener.Close()

	udpAddr := fmt.Sprintf(":%d", port)
	udpConn, err := net.ListenPacket("udp", udpAddr)
	if err != nil {
		return false, fmt.Sprintf("UDP端口被占用: %v", err)
	}
	udpConn.Close()

	return true, "端口可用"
}

func (pm *PortManager) GetServerPortUsage() map[string]interface{} {
	pm.Lock()
	defer pm.Unlock()

	spm := pm.ServerPortManager
	total := int(spm.PortRange.End - spm.PortRange.Start + 1)
	used := len(spm.usedPorts)

	return map[string]interface{}{
		"total":     total,
		"used":      used,
		"available": total - used,
		"usage":     float64(used) / float64(total) * 100,
		"range":     fmt.Sprintf("%d-%d", spm.PortRange.Start, spm.PortRange.End),
	}
}

// GetClientPortUsage 获取客户端端口使用情况
func (pm *PortManager) GetClientPortUsage() map[string]interface{} {
	pm.Lock()
	defer pm.Unlock()

	cpm := pm.ClientPortManager
	total := int(cpm.defaultPortRange.End - cpm.defaultPortRange.Start + 1)
	used := len(cpm.usedPorts)

	return map[string]interface{}{
		"total":     total,
		"used":      used,
		"available": total - used,
		"usage":     float64(used) / float64(total) * 100,
		"clients":   len(cpm.allocatedClients),
	}
}

// GetClientPorts 获取指定客户端占用的端口
func (pm *PortManager) GetClientPorts(clientID uint) []uint16 {
	pm.Lock()
	defer pm.Unlock()

	if ports, ok := pm.ClientPortManager.allocatedClients[clientID]; ok {
		result := make([]uint16, len(ports))
		copy(result, ports)
		return result
	}
	return []uint16{}
}

// IsServerPortUsed 检查服务器端口是否被使用
func (pm *PortManager) IsServerPortUsed(port uint16) bool {
	pm.Lock()
	defer pm.Unlock()
	return pm.ServerPortManager.usedPorts[port]
}

// IsClientPortUsed 检查客户端端口是否被使用
func (pm *PortManager) IsClientPortUsed(port uint16) (bool, uint) {
	pm.Lock()
	defer pm.Unlock()
	if clientID, used := pm.ClientPortManager.usedPorts[port]; used {
		return true, clientID
	}
	return false, 0
}

// SetCustomPortRange 设置客户端自定义端口范围
func (pm *PortManager) SetCustomPortRange(clientID uint, start, end uint16) error {
	pm.Lock()
	defer pm.Unlock()

	if start == 0 || end == 0 || start > end {
		return fmt.Errorf("invalid port range: %d - %d", start, end)
	}

	pm.ClientPortManager.customPortRanges[clientID] = PortRange{
		Start: start,
		End:   end,
	}
	return nil
}

// RemoveCustomPortRange 移除客户端自定义端口范围
func (pm *PortManager) RemoveCustomPortRange(clientID uint) {
	pm.Lock()
	defer pm.Unlock()
	delete(pm.ClientPortManager.customPortRanges, clientID)
}

func (pm *PortManager) AllocClientPort(clientID uint, mode string) (uint16, error) {
	pm.Lock()
	defer pm.Unlock()

	cpm := pm.ClientPortManager

	// 获取该 client 的端口范围
	start, end := cpm.getPortRange(clientID)
	if start == 0 || end == 0 || start > end {
		return 0, fmt.Errorf("invalid port range for client %d: %d - %d", clientID, start, end)
	}

	// 检查该客户端是否已有分配的端口
	if ports, ok := cpm.allocatedClients[clientID]; ok && len(ports) > 0 {
		// 返回第一个已分配的端口
		return ports[0], nil
	}

	// 分配新端口
	var port uint16
	var found bool

	switch mode {
	case randomAlloc:
		for i := 0; i <= int(end-start); i++ {
			port = uint16(rand.Intn(int(end-start)+1)) + start
			if _, used := cpm.usedPorts[port]; !used {
				if ok, _ := CheckPortAvailable(port); ok {
					found = true
					break
				}
			}
		}

	case sequentialAlloc:
		current := cpm.lastAllocatedPort
		if current < start || current > end {
			current = start
		}

		for i := 0; i <= int(end-start); i++ {
			port = ((current-start)+1+uint16(i))%(end-start+1) + start
			if _, used := cpm.usedPorts[port]; !used {
				if ok, _ := CheckPortAvailable(port); ok {
					cpm.lastAllocatedPort = port
					found = true
					break
				}
			}
		}
	}

	if found {
		cpm.usedPorts[port] = clientID
		if _, exists := cpm.allocatedClients[clientID]; !exists {
			cpm.allocatedClients[clientID] = make([]uint16, 0)
		}
		cpm.allocatedClients[clientID] = append(cpm.allocatedClients[clientID], port)
		return port, nil
	}

	return 0, fmt.Errorf("no available port for client %d in range %d - %d", clientID, start, end)
}

// 获取 client 的端口范围（优先使用自定义范围）
func (cpm *ClientPortManager) getPortRange(clientID uint) (start, end uint16) {
	if customRange, ok := cpm.customPortRanges[clientID]; ok {
		return customRange.Start, customRange.End
	}
	return cpm.defaultPortRange.Start, cpm.defaultPortRange.End
}

// ReleaseClientPort 释放 client 占用的指定端口
func (pm *PortManager) ReleaseClientPort(clientID uint, port uint16) {
	pm.Lock()
	defer pm.Unlock()

	cpm := pm.ClientPortManager
	if ports, ok := cpm.allocatedClients[clientID]; ok {
		// 从 usedPorts 中删除该端口
		delete(cpm.usedPorts, port)

		// 从客户端的端口列表中移除该端口
		for i, p := range ports {
			if p == port {
				cpm.allocatedClients[clientID] = append(ports[:i], ports[i+1:]...)
				break
			}
		}

		// 如果客户端没有端口了，删除客户端记录
		if len(cpm.allocatedClients[clientID]) == 0 {
			delete(cpm.allocatedClients, clientID)
		}
	}
}

// ReleaseAllClientPorts 释放 client 占用的所有端口
func (pm *PortManager) ReleaseAllClientPorts(clientID uint) {
	pm.Lock()
	defer pm.Unlock()

	cpm := pm.ClientPortManager
	if ports, ok := cpm.allocatedClients[clientID]; ok {
		// 释放该客户端的所有端口
		for _, port := range ports {
			delete(cpm.usedPorts, port)
		}
		delete(cpm.allocatedClients, clientID)
	}
}

func (pm *PortManager) OccupyTheClientPort(clientID uint, port uint16) {
	pm.Lock()
	defer pm.Unlock()

	cpm := pm.ClientPortManager
	cpm.usedPorts[port] = clientID

	// 更新客户端分配的端口列表
	if _, exists := cpm.allocatedClients[clientID]; !exists {
		cpm.allocatedClients[clientID] = make([]uint16, 0)
	}
	cpm.allocatedClients[clientID] = append(cpm.allocatedClients[clientID], port)
}

func (pm *PortManager) ReleaseServerPort(port uint16) {
	pm.Lock()
	defer pm.Unlock()

	spm := pm.ServerPortManager
	if spm.lastAllocatedPort == port {
		spm.lastAllocatedPort -= 1
	}
	delete(spm.usedPorts, port)
}
