package tcp

import (
	"context"
	"encoding/json"
	"math/rand"
	"net"
	"server/global"
	"server/model/basic"
	"server/model/request/heartbeat"
	"server/model/tlv"
	"sync"
	"time"

	"go.uber.org/zap"
)

// HeartbeatManager 心跳管理器
type HeartbeatManager struct {
	listener         *TCPListener
	activeHeartbeats map[string]*HeartbeatSession // remoteAddr -> HeartbeatSession
	mutex            sync.RWMutex
	ctx              context.Context
	cancel           context.CancelFunc
}

// HeartbeatSession 单个客户端的心跳会话
type HeartbeatSession struct {
	remoteAddr      string
	conn            net.Conn
	metadata        *tlv.METADATA
	messageHandler  *TLVMessageHandler
	pingDuration    time.Duration
	maxTimeoutCount int
	timeoutCount    int
	ctx             context.Context
	cancel          context.CancelFunc
	lastActiveAt    time.Time
}

// NewHeartbeatManager 创建心跳管理器
func NewHeartbeatManager(listener *TCPListener) *HeartbeatManager {
	ctx, cancel := context.WithCancel(context.Background())
	return &HeartbeatManager{
		listener:         listener,
		activeHeartbeats: make(map[string]*HeartbeatSession),
		ctx:              ctx,
		cancel:           cancel,
	}
}

// Start 启动心跳管理器
func (hm *HeartbeatManager) Start() {
	global.LOG.Info("心跳管理器已启动")
}

// Stop 停止心跳管理器
func (hm *HeartbeatManager) Stop() {
	hm.cancel()

	hm.mutex.Lock()
	defer hm.mutex.Unlock()

	// 停止所有活跃的心跳会话
	for remoteAddr, session := range hm.activeHeartbeats {
		session.cancel()
		delete(hm.activeHeartbeats, remoteAddr)
	}

	global.LOG.Info("心跳管理器已停止")
}

// StartHeartbeatForClient 为客户端启动心跳检测
func (hm *HeartbeatManager) StartHeartbeatForClient(remoteAddr string, conn net.Conn, metadata *tlv.METADATA, messageHandler *TLVMessageHandler) {
	hm.mutex.Lock()
	defer hm.mutex.Unlock()

	// 如果已经存在，先停止旧的
	if existingSession, exists := hm.activeHeartbeats[remoteAddr]; exists {
		existingSession.cancel()
		delete(hm.activeHeartbeats, remoteAddr)
		global.LOG.Warn("客户端重复注册，停止旧的心跳会话", zap.String("remoteAddr", remoteAddr))
	}

	// 获取心跳配置
	pingDuration := time.Duration(hm.listener.PingDuration) * time.Second
	if pingDuration == 0 {
		pingDuration = 30 * time.Second
	}

	maxTimeoutCount := hm.listener.MaxTimeoutCount
	if maxTimeoutCount == 0 {
		maxTimeoutCount = 5
	}

	// 创建新的心跳会话
	ctx, cancel := context.WithCancel(hm.ctx)
	session := &HeartbeatSession{
		remoteAddr:      remoteAddr,
		conn:            conn,
		metadata:        metadata,
		messageHandler:  messageHandler,
		pingDuration:    pingDuration,
		maxTimeoutCount: maxTimeoutCount,
		timeoutCount:    0,
		ctx:             ctx,
		cancel:          cancel,
		lastActiveAt:    time.Now(),
	}

	hm.activeHeartbeats[remoteAddr] = session

	// 启动心跳检测 goroutine
	go hm.runHeartbeatSession(session)

	global.LOG.Info("为客户端启动心跳检测",
		zap.String("remoteAddr", remoteAddr),
		zap.Duration("interval", pingDuration),
		zap.Int("maxTimeoutCount", maxTimeoutCount))
}

// StopHeartbeatForClient 停止客户端的心跳检测
func (hm *HeartbeatManager) StopHeartbeatForClient(remoteAddr string) {
	hm.mutex.Lock()
	defer hm.mutex.Unlock()

	if session, exists := hm.activeHeartbeats[remoteAddr]; exists {
		session.cancel()
		delete(hm.activeHeartbeats, remoteAddr)
		global.LOG.Info("停止客户端心跳检测", zap.String("remoteAddr", remoteAddr))
	}
}

// UpdateClientActivity 更新客户端活动时间（收到PONG时调用）
func (hm *HeartbeatManager) UpdateClientActivity(remoteAddr string) {
	hm.mutex.RLock()
	session, exists := hm.activeHeartbeats[remoteAddr]
	hm.mutex.RUnlock()

	if exists {
		oldTimeoutCount := session.timeoutCount
		session.timeoutCount = 0
		session.lastActiveAt = time.Now()

		if oldTimeoutCount > 0 {
			global.LOG.Info("🔄 重置客户端超时计数",
				zap.String("remoteAddr", remoteAddr),
				zap.Int("oldTimeoutCount", oldTimeoutCount))
		} else {
			global.LOG.Debug("💓 更新客户端活动时间", zap.String("remoteAddr", remoteAddr))
		}
	} else {
		global.LOG.Warn("⚠️ 尝试更新不存在的客户端活动时间", zap.String("remoteAddr", remoteAddr))
	}
}

// runHeartbeatSession 运行单个客户端的心跳会话
func (hm *HeartbeatManager) runHeartbeatSession(session *HeartbeatSession) {
	defer func() {
		// 会话结束时清理
		hm.mutex.Lock()
		delete(hm.activeHeartbeats, session.remoteAddr)
		hm.mutex.Unlock()
		global.LOG.Info("心跳会话已结束", zap.String("remoteAddr", session.remoteAddr))
	}()

	// 添加随机抖动
	jitteredDuration := hm.addJitterToInterval(session.pingDuration)
	ticker := time.NewTicker(jitteredDuration)
	defer ticker.Stop()

	global.LOG.Info("心跳会话已启动",
		zap.String("remoteAddr", session.remoteAddr),
		zap.Duration("jitteredInterval", jitteredDuration))

	for {
		select {
		case <-session.ctx.Done():
			return
		case <-ticker.C:
			if err := hm.sendHeartbeat(session); err != nil {
				session.timeoutCount++
				global.LOG.Warn("发送心跳失败",
					zap.String("remoteAddr", session.remoteAddr),
					zap.Int("timeoutCount", session.timeoutCount),
					zap.Error(err))

				if session.timeoutCount >= session.maxTimeoutCount {
					global.LOG.Info("心跳超时次数达到上限，关闭连接",
						zap.String("remoteAddr", session.remoteAddr),
						zap.Int("finalTimeoutCount", session.timeoutCount))

					// 关闭连接并清理客户端数据
					session.conn.Close()
					hm.listener.removeClient(session.remoteAddr)
					return
				}
			} else {
				// 心跳发送成功，重置超时计数
				if session.timeoutCount > 0 {
					session.timeoutCount = 0
					global.LOG.Debug("心跳恢复正常", zap.String("remoteAddr", session.remoteAddr))
				}
			}
		}
	}
}

// sendHeartbeat 发送结构化心跳包
func (hm *HeartbeatManager) sendHeartbeat(session *HeartbeatSession) error {
	// 创建结构化心跳请求
	heartbeatReq := hm.createHeartbeatRequest(session.remoteAddr)

	// 序列化心跳数据
	heartbeatData, err := hm.serializeHeartbeatRequest(heartbeatReq)
	if err != nil {
		return err
	}

	// 创建TLV包
	heartbeatPacket, err := session.messageHandler.CreateAdvancedHeartbeatPacket(heartbeatData, tlv.PING)
	if err != nil {
		return err
	}

	data := heartbeatPacket.Serialize()
	if _, err := session.conn.Write(data); err != nil {
		return err
	}

	return nil
}

// addJitterToInterval 为心跳间隔添加随机抖动
func (hm *HeartbeatManager) addJitterToInterval(baseInterval time.Duration) time.Duration {
	// 添加±20%的随机抖动
	jitterPercent := 0.2
	jitter := time.Duration(float64(baseInterval) * jitterPercent * (rand.Float64()*2 - 1))
	result := baseInterval + jitter

	// 确保结果不小于5秒
	if result < 5*time.Second {
		result = 5 * time.Second
	}

	return result
}

// GetActiveHeartbeatCount 获取活跃心跳会话数量
func (hm *HeartbeatManager) GetActiveHeartbeatCount() int {
	hm.mutex.RLock()
	defer hm.mutex.RUnlock()
	return len(hm.activeHeartbeats)
}

// createHeartbeatRequest 创建结构化心跳请求
func (hm *HeartbeatManager) createHeartbeatRequest(remoteAddr string) *heartbeat.HeartbeatRequest {
	return &heartbeat.HeartbeatRequest{
		ClientID:    remoteAddr,
		Timestamp:   time.Now(),
		SequenceNum: hm.generateSequenceNumber(),
		SystemInfo:  hm.getServerSystemStatus(),
		NetworkInfo: hm.getServerNetworkStatus(),
		Type:        tlv.PING,
		Jitter:      hm.generateJitter(),
	}
}

// serializeHeartbeatRequest 序列化心跳请求
func (hm *HeartbeatManager) serializeHeartbeatRequest(req *heartbeat.HeartbeatRequest) ([]byte, error) {
	return json.Marshal(req)
}

// generateSequenceNumber 生成序列号
func (hm *HeartbeatManager) generateSequenceNumber() uint64 {
	return uint64(time.Now().UnixNano())
}

// generateJitter 生成随机抖动值（毫秒）
func (hm *HeartbeatManager) generateJitter() int {
	return rand.Intn(5000)
}

// getServerSystemStatus 获取服务器系统状态
func (hm *HeartbeatManager) getServerSystemStatus() basic.SystemStatus {
	// TODO: 实现真实的系统状态获取
	return basic.SystemStatus{
		CPUUsage:    0.0, // 可以集成系统监控
		MemoryUsage: 0.0,
		DiskUsage:   0.0,
		Uptime:      0,
		LoadAvg:     0.0,
	}
}

// getServerNetworkStatus 获取服务器网络状态
func (hm *HeartbeatManager) getServerNetworkStatus() basic.NetworkStatus {
	return basic.NetworkStatus{
		LocalIP:    hm.listener.LocalListenAddr,
		PublicIP:   "", // 可以通过外部API获取
		Latency:    0,
		PacketLoss: 0.0,
		Bandwidth:  0,
	}
}
