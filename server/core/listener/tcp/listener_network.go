package tcp

import (
	"server/core/manager"
	"server/global"
	networkResp "server/model/response/network"
	"server/model/tlv"
	"server/utils"

	"go.uber.org/zap"
)

// handleNetworkPacket 处理网络监控数据包
func (l *TCPListener) handleNetworkPacket(remoteAddr string, packet *tlv.Packet) error {
	global.LOG.Info("🌐 收到网络监控数据包",
		zap.String("remoteAddr", remoteAddr),
		zap.Uint8("code", packet.Header.Code),
		zap.Int("dataSize", len(packet.PacketData.Data)))

	// 🔧 重要修复：更新客户端活动时间，防止心跳超时断开连接
	l.heartbeatManager.UpdateClientActivity(remoteAddr)
	global.LOG.Info("✅ 已更新客户端活动时间", zap.String("remoteAddr", remoteAddr))

	// 根据Code字段判断网络监控操作类型
	global.LOG.Info("🔍 开始处理网络监控操作",
		zap.String("remoteAddr", remoteAddr),
		zap.Uint8("code", packet.Header.Code))

	var err error
	switch packet.Header.Code {
	case tlv.NetStatsCmd:
		global.LOG.Info("📊 处理网络统计信息响应", zap.String("remoteAddr", remoteAddr))
		err = l.handleNetworkOperationResponse(remoteAddr, packet, "network_stats")
	case tlv.NetInterfacesCmd:
		global.LOG.Info("🌐 处理网络接口信息响应", zap.String("remoteAddr", remoteAddr))
		err = l.handleNetworkOperationResponse(remoteAddr, packet, "network_interfaces")
	case tlv.NetConnectionsCmd:
		global.LOG.Info("🔗 处理网络连接信息响应", zap.String("remoteAddr", remoteAddr))
		err = l.handleNetworkOperationResponse(remoteAddr, packet, "network_connections")
	case tlv.NetCloseConnCmd:
		global.LOG.Info("❌ 处理关闭网络连接响应", zap.String("remoteAddr", remoteAddr))
		err = l.handleNetworkOperationResponse(remoteAddr, packet, "close_connection")
	case tlv.NetProgressCmd:
		global.LOG.Info("📊 处理网络监控进度数据", zap.String("remoteAddr", remoteAddr))
		err = l.handleNetworkProgressData(remoteAddr, packet)
	default:
		global.LOG.Warn("❓ 未知的网络监控操作类型",
			zap.String("remoteAddr", remoteAddr),
			zap.Uint8("code", packet.Header.Code))
		return nil
	}

	if err != nil {
		global.LOG.Error("❌ 网络监控操作处理失败",
			zap.String("remoteAddr", remoteAddr),
			zap.Uint8("code", packet.Header.Code),
			zap.Error(err))
		return err
	}

	global.LOG.Info("✅ 网络监控操作处理成功",
		zap.String("remoteAddr", remoteAddr),
		zap.Uint8("code", packet.Header.Code))
	return nil
}

// handleNetworkOperationResponse 处理网络监控操作响应
func (l *TCPListener) handleNetworkOperationResponse(remoteAddr string, packet *tlv.Packet, operation string) error {
	switch operation {
	case "network_stats":
		// 反序列化网络统计信息响应
		global.LOG.Info("📊 开始反序列化网络统计信息响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Int("dataSize", len(packet.PacketData.Data)))

		// 添加原始数据日志用于调试
		global.LOG.Debug("📄 原始响应数据",
			zap.String("remoteAddr", remoteAddr),
			zap.String("rawData", string(packet.PacketData.Data)))

		var response networkResp.NetworkStatsResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("❌ 反序列化网络统计信息响应失败",
				zap.Error(err),
				zap.String("remoteAddr", remoteAddr),
				zap.String("rawData", string(packet.PacketData.Data)))
			return err
		}

		global.LOG.Info("✅ 反序列化网络统计信息响应成功",
			zap.String("remoteAddr", remoteAddr),
			zap.Uint64("taskID", response.TaskID),
			zap.Bool("success", response.Success))

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "network_stats", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		if response.Error != "" {
			global.LOG.Warn("客户端网络统计信息操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("网络统计信息响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.Bool("success", response.Success))

			// 🔧 处理成功后再次更新活动时间，确保连接保持活跃
			l.heartbeatManager.UpdateClientActivity(remoteAddr)
		}

	case "network_interfaces":
		// 反序列化网络接口信息响应
		var response networkResp.NetworkInterfacesResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化网络接口信息响应失败",
				zap.Error(err),
				zap.String("remoteAddr", remoteAddr),
				zap.String("rawData", string(packet.PacketData.Data)))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "network_interfaces", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		if response.Error != "" {
			global.LOG.Warn("客户端网络接口信息操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("网络接口信息响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.Int("interfaceCount", len(response.Interfaces)))

			// 🔧 处理成功后再次更新活动时间，确保连接保持活跃
			l.heartbeatManager.UpdateClientActivity(remoteAddr)
		}

	case "network_connections":
		// 反序列化网络连接信息响应
		var response networkResp.NetworkConnectionsResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化网络连接信息响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "network_connections", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		if response.Error != "" {
			global.LOG.Warn("客户端网络连接信息操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("网络连接信息响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.Int("connectionCount", len(response.Connections)))

			// 🔧 处理成功后再次更新活动时间，确保连接保持活跃
			l.heartbeatManager.UpdateClientActivity(remoteAddr)
		}

	case "close_connection":
		// 反序列化关闭网络连接响应
		var response networkResp.CloseConnectionResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化关闭网络连接响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "close_connection", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		if response.Error != "" {
			global.LOG.Warn("客户端关闭网络连接操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("关闭网络连接响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.Bool("success", response.Success))

			// 🔧 处理成功后再次更新活动时间，确保连接保持活跃
			l.heartbeatManager.UpdateClientActivity(remoteAddr)
		}

	default:
		global.LOG.Warn("未知的网络监控操作类型", zap.String("remoteAddr", remoteAddr), zap.String("operation", operation))
	}

	return nil
}

// handleNetworkProgressData 处理网络监控进度数据
func (l *TCPListener) handleNetworkProgressData(remoteAddr string, packet *tlv.Packet) error {
	// 反序列化进度数据
	var progressData map[string]interface{}
	if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &progressData); err != nil {
		global.LOG.Error("反序列化网络监控进度数据失败",
			zap.Error(err),
			zap.String("remoteAddr", remoteAddr))
		return err
	}

	global.LOG.Info("📊 收到网络监控进度数据",
		zap.String("remoteAddr", remoteAddr),
		zap.Any("progress", progressData))

	// 广播进度数据
	l.broadcastInterfaceProgress(remoteAddr, progressData)
	return nil
}

// broadcastInterfaceProgress 广播网络接口处理进度
func (l *TCPListener) broadcastInterfaceProgress(remoteAddr string, progressData map[string]interface{}) {
	global.LOG.Info("📡 广播网络接口处理进度",
		zap.String("remoteAddr", remoteAddr),
		zap.Any("progress", progressData))

	// 使用进度管理器更新并广播进度
	progressManager := manager.GetProgressManager()
	progressManager.UpdateProgress(remoteAddr, progressData)
}
