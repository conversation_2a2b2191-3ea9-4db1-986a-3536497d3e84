package tcp

import (
	"crypto/rand"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"server/core/workerpool"
	"server/global"
	"server/model/sys"

	"go.uber.org/zap"

	"sync"
)

// TCPListenerManager 管理所有的tcp监听器
type TCPListenerManager struct {
	listeners map[uint]*TCPListener
	mutex     sync.Mutex
}

// 全局的TCP监听器管理器
var TCPManager = &TCPListenerManager{
	listeners: make(map[uint]*TCPListener),
}

// StartListener 启动一个TCP监听器
func (m *TCPListenerManager) StartListener(listener sys.Listener) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查监听器是否已存在
	if _, exists := m.listeners[listener.ID]; exists {
		return fmt.Errorf("监听器 %d 已存在", listener.ID)
	}

	// 创建TCP监听器
	tcpListener, err := net.Listen("tcp", listener.LocalListenAddr)
	if err != nil {
		return fmt.Errorf("创建TCP监听器失败: %s", err.Error())
	}
	// 生成随机文件名前缀
	prefixBytes := make([]byte, 8)
	_, err = rand.Read(prefixBytes)
	if err != nil {
		return fmt.Errorf("生成随机文件名前缀失败: %s", err.Error())
	}
	prefix := fmt.Sprintf("%x", prefixBytes)

	// 创建TCPListener实例
	l := &TCPListener{
		ID:                listener.ID,
		LocalListenAddr:   listener.LocalListenAddr,
		RemoteConnectAddr: listener.RemoteConnectAddr,
		Status:            listener.Status,
		Listener:          tcpListener,
		maxConnections:    1000, // 设置最大连接数
		Key:               listener.Key,
		Salt:              listener.Salt,
		PingDuration:      listener.PingDuration,
		MaxTimeoutCount:   listener.MaxTimeoutCount,
		ClientPrefix:      prefix,
		Clients:           make(map[string]string),
		shutdownCh:        make(chan struct{}),
		memoryPool:        NewMemoryPool(),
	}

	// 初始化心跳管理器
	l.heartbeatManager = NewHeartbeatManager(l)
	l.heartbeatManager.Start()

	// 启动定期清理机制
	l.startCleanup()

	if err = l.initRSAKeys(); err != nil {
		global.LOG.Error("初始化RSA密钥失败", zap.Error(err))
		return err
	}

	// 🚀 使用文件I/O工作池生成客户端，避免阻塞主线程

	// 生成Linux客户端
	linuxTask := workerpool.NewFileIOTask("generate_linux_client", func() error {
		if err := l.generateLinuxClient(); err != nil {
			global.LOG.Error("生成Linux客户端失败", zap.Error(err))
			return err
		}
		global.LOG.Info("Linux客户端生成成功")
		return nil
	})

	// 生成Windows客户端
	windowsTask := workerpool.NewFileIOTask("generate_windows_client", func() error {
		if err := l.generateWindowsClient(); err != nil {
			global.LOG.Error("生成Windows客户端失败", zap.Error(err))
			return err
		}
		global.LOG.Info("Windows客户端生成成功")
		return nil
	})

	// 生成Darwin客户端
	darwinTask := workerpool.NewFileIOTask("generate_darwin_client", func() error {
		if err := l.generateDarwinClient(); err != nil {
			global.LOG.Error("生成Darwin客户端失败", zap.Error(err))
			return err
		}
		global.LOG.Info("Darwin客户端生成成功")
		return nil
	})

	// 提交所有任务到文件I/O工作池
	if err := workerpool.SubmitFileIOTask(linuxTask); err != nil {
		global.LOG.Error("提交Linux客户端生成任务失败", zap.Error(err))
	}
	if err := workerpool.SubmitFileIOTask(windowsTask); err != nil {
		global.LOG.Error("提交Windows客户端生成任务失败", zap.Error(err))
	}
	if err := workerpool.SubmitFileIOTask(darwinTask); err != nil {
		global.LOG.Error("提交Darwin客户端生成任务失败", zap.Error(err))
	}
	fmt.Println(l.Clients)
	// 添加到管理器
	m.listeners[listener.ID] = l

	// 注册到统计管理器
	RegisterListener(l)

	// 启动监听器
	go l.handleConnections()

	global.LOG.Info(fmt.Sprintf("TCP监听器 %d 已启动在 %s，客户端文件名前缀: %s", listener.ID, listener.LocalListenAddr, prefix))

	return nil
}

// CleanClientBinFiles 清理客户端二进制文件目录
func CleanClientBinFiles(clientFiles map[string]string) error {
	// 如果没有指定客户端文件列表，则不执行任何操作
	if clientFiles == nil || len(clientFiles) == 0 {
		return nil
	}

	// 确保CLIENT_BIN_DIR已初始化
	if global.CLIENT_BIN_DIR == "" {
		return fmt.Errorf("客户端二进制文件目录未初始化")
	}

	// 删除指定的客户端文件
	for _, fileName := range clientFiles {
		filePath := filepath.Join(global.CLIENT_BIN_DIR, fileName)
		if err := os.Remove(filePath); err != nil {
			if !os.IsNotExist(err) {
				global.LOG.Error(fmt.Sprintf("删除客户端文件失败: %s, 错误: %s", filePath, err.Error()))
			}
		} else {
			global.LOG.Info(fmt.Sprintf("成功删除客户端文件: %s", filePath))
		}
	}

	return nil
}

// StopListener 停止一个TCP监听器
func (m *TCPListenerManager) StopListener(listenerID uint) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查监听器是否存在
	l, exists := m.listeners[listenerID]
	if !exists {
		return fmt.Errorf("监听器 %d 不存在", listenerID)
	}

	// 停止心跳管理器
	if l.heartbeatManager != nil {
		l.heartbeatManager.Stop()
	}

	// 停止清理机制
	l.stopCleanup()

	// 关闭所有连接
	l.connections.Range(func(key, value interface{}) bool {
		addr := key.(string)
		connInfo := value.(*ConnectionInfo)
		connInfo.Conn.Close()
		l.connections.Delete(addr)
		return true
	})

	// 关闭HTTP服务器
	if l.httpServer != nil {
		if err := l.httpServer.Close(); err != nil {
			global.LOG.Error(fmt.Sprintf("关闭HTTP服务器失败: %s", err.Error()))
		}
	}

	// 关闭监听器
	if err := l.Listener.Close(); err != nil {
		return fmt.Errorf("关闭监听器失败: %s", err.Error())
	}

	// 清理客户端文件
	if err := CleanClientBinFiles(l.Clients); err != nil {
		global.LOG.Error(fmt.Sprintf("清理客户端文件失败: %s", err.Error()))
	}

	// 从统计管理器中注销
	UnregisterListener(listenerID)

	// 从管理器中移除
	delete(m.listeners, listenerID)

	global.LOG.Info(fmt.Sprintf("TCP监听器 %d 已停止", listenerID))

	return nil
}

// GetListener 获取一个TCP监听器
func (m *TCPListenerManager) GetListener(listenerID uint) (*TCPListener, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 检查监听器是否存在
	l, exists := m.listeners[listenerID]
	if !exists {
		return nil, fmt.Errorf("监听器 %d 不存在", listenerID)
	}

	return l, nil
}
