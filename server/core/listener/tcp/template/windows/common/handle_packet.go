//go:build windows
// +build windows

package common

import (
	"encoding/json"
	"log"
)

// ProcessIncomingPacket 处理接收到的数据包
func (cm *ConnectionManager) ProcessIncomingPacket(packetBytes []byte) (*Packet, bool, error) {
	//packet := &Packet{}
	packet := cm.PacketPool.Get().(*Packet)
	defer cm.PacketPool.Put(packet)
	if err := packet.DeserializePacket(packetBytes); err != nil {
		return nil, false, err
	}

	if err := packet.DecryptPacket(cm.metadata); err != nil {
		return nil, false, err
	}

	// 处理分片
	if packet.Header.Flags == MoreFrag || packet.Header.Flags == NoMoreFrag {
		return cm.handleFragmentedPacket(packet)
	}

	return packet, true, nil
}

func (cm *ConnectionManager) processPacket(packet *Packet) {
	switch {
	case packet.Header.Type == RunCommand && packet.Header.Code == ExecInput:
		cm.ptyInputChan <- packet.PacketData.Data

	case packet.Header.Type == Registration && packet.Header.Code == RegResponse:
		log.Println("已成功注册!")

	case packet.Header.Type == File:
		go cm.handleFileRequest(packet)
	case packet.Header.Type == Dir:
		go cm.handleDirRequest(packet)
	case packet.Header.Type == Heartbeat && packet.Header.Code == PING:
		// 收到服务器的PING，回复结构化PONG
		heartbeatReq := cm.createClientHeartbeatRequest()
		heartbeatReq.Type = PONG // 设置为PONG类型

		heartbeatData, err := json.Marshal(heartbeatReq)
		if err != nil {
			log.Printf("序列化PONG数据失败: %v", err)
			return
		}

		pong, err := cm.createAdvancedHeartbeatPacket(heartbeatData, PONG)
		if err != nil {
			log.Printf("创建PONG包失败: %v", err)
			return
		}

		pongBytes := pong.Serialize()
		_, err = cm.conn.Write(pongBytes)
		if err != nil {
			log.Println("发送PONG响应失败: ", err)
		} else {
			log.Println("已回复服务器PING（结构化数据）")
		}
	case packet.Header.Type == Heartbeat && packet.Header.Code == PONG:
		// 收到服务器对客户端PING的PONG响应（可能包含结构化数据）
		log.Println("收到服务器PONG响应，心跳正常")

		// 尝试解析结构化响应数据
		if len(packet.PacketData.Data) > 0 {
			cm.parseServerHeartbeatResponse(packet.PacketData.Data)
		}
	case packet.Header.Type == TermResize && packet.Header.Code == WinResize: // 新增终端调整类型
		go cm.handleTermResize(packet)
	case packet.Header.Type == Process:
		go cm.handleProcessRequest(packet)
	case packet.Header.Type == Screenshot:
		go cm.handleScreenshotRequest(packet)
	case packet.Header.Type == Network:
		go cm.handleNetworkRequest(packet)
	default:
		log.Printf("未知的数据包类型: Type=%d, Code=%d", packet.Header.Type, packet.Header.Code)
	}
}

// handleFragmentedPacket 处理分片数据包
func (cm *ConnectionManager) handleFragmentedPacket(packet *Packet) (*Packet, bool, error) {
	cm.fragMutex.Lock()
	defer cm.fragMutex.Unlock()

	label := packet.Header.Label
	buffer, exists := cm.fragmentBuffer[label]
	if !exists {
		buffer = NewFragmentBuffer()
		cm.fragmentBuffer[label] = buffer
	}

	data, complete := buffer.AddFragment(packet.Header.FragIndex, packet.PacketData.Data, packet.Header.Flags == NoMoreFrag)
	if complete {
		delete(cm.fragmentBuffer, label)
		packet.PacketData.Data = data
		packet.Header.Flags = NoMoreFrag
		packet.Header.FragIndex = 0
		return packet, true, nil
	}

	return nil, false, nil
}
