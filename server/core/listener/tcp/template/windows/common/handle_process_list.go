//go:build windows
// +build windows

package common

import (
	"encoding/json"
	"fmt"
	"log"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"unsafe"

	"github.com/shirou/gopsutil/v3/process"
	"golang.org/x/sys/windows"
)

// ProcessFullInfo 进程信息结构体
type ProcessFullInfo struct {
	PID           int32     `json:"pid"`
	PPID          int32     `json:"ppid"`
	Name          string    `json:"name"`
	Executable    string    `json:"executable"`
	User          string    `json:"username"`
	Status        string    `json:"status"`
	CPU           float64   `json:"cpu_percent"`
	Memory        uint64    `json:"memory_mb"`
	MemoryPercent float32   `json:"memoryPercent"`
	Priority      int32     `json:"priority"`
	StartTime     time.Time `json:"create_time"`
	RunTime       string    `json:"runTime"`
	Cmdline       string    `json:"command_line"`
	System        bool      `json:"system"`
}

// ProcessListRequest 进程列表请求
type ProcessListRequest struct {
	TaskID     uint64 `json:"task_id"` // 任务ID
	ShowSystem bool   `json:"showSystem"`
}

// ProcessListResponse 进程列表响应
type ProcessListResponse struct {
	TaskID    uint64            `json:"task_id"` // 任务ID
	Success   bool              `json:"success"` // 操作是否成功
	Processes []ProcessFullInfo `json:"processes"`
	Error     string            `json:"error"` // 错误信息
	Count     int               `json:"count"` // 进程总数
}

// Windows进程状态常量
var WORKER_POOL_SIZE = runtime.NumCPU() * 2 // 并发工作池大小
const (
	STILL_ACTIVE = 259

	// 缓存清理相关常量
	CACHE_CLEANUP_INTERVAL = 30 * time.Second // 缓存清理间隔

	// 性能优化常量
	BATCH_SIZE                = 40   // 批处理大小
	MAX_PROCESSES_PER_REQUEST = 1000 // 单次请求最大进程数，与Linux客户端保持一致

	// Windows API 常量
	PROCESS_QUERY_INFORMATION = 0x0400
	ProcessBasicInformation   = 0
)

// PROCESS_BASIC_INFORMATION 结构体
type PROCESS_BASIC_INFORMATION struct {
	ExitStatus                   uintptr
	PebBaseAddress               uintptr
	AffinityMask                 uintptr
	BasePriority                 uintptr
	UniqueProcessId              uintptr
	InheritedFromUniqueProcessId uintptr
}

// Windows API 函数声明
var (
	ntdll                         = windows.NewLazySystemDLL("ntdll.dll")
	procNtQueryInformationProcess = ntdll.NewProc("NtQueryInformationProcess")
)

// getWindowsPPIDOptimized 使用Windows API直接获取父进程ID - 性能优化版本
func (cm *ConnectionManager) getWindowsPPIDOptimized(pid uint32) int32 {
	// 快速判断：系统关键进程
	if pid <= 4 {
		return 0
	}

	// 检查缓存 - 带LRU访问时间记录
	ppidCacheMutex.Lock() // 使用写锁，因为需要更新访问时间
	if cachedPPID, exists := ppidCache[pid]; exists {
		// 更新访问时间
		ppidAccessTimes[pid] = time.Now()
		ppidCacheMutex.Unlock()
		return cachedPPID
	}
	ppidCacheMutex.Unlock()

	// 尝试打开进程句柄
	handle, err := windows.OpenProcess(PROCESS_QUERY_INFORMATION, false, pid)
	if err != nil {
		// 权限不足时尝试使用有限权限
		handle, err = windows.OpenProcess(windows.PROCESS_QUERY_LIMITED_INFORMATION, false, pid)
		if err != nil {
			// 缓存错误结果，避免重复尝试
			ppidCacheMutex.Lock()
			ppidCache[pid] = 0
			ppidCacheMutex.Unlock()
			return 0
		}
	}
	defer windows.CloseHandle(handle)

	// 调用NtQueryInformationProcess获取基本信息
	var pbi PROCESS_BASIC_INFORMATION
	var returnLength uint32

	r1, _, _ := procNtQueryInformationProcess.Call(
		uintptr(handle),
		uintptr(ProcessBasicInformation),
		uintptr(unsafe.Pointer(&pbi)),
		uintptr(unsafe.Sizeof(pbi)),
		uintptr(unsafe.Pointer(&returnLength)),
	)

	// 检查调用是否成功 (NT_SUCCESS)
	var ppid int32
	if r1 != 0 {
		ppid = 0
	} else {
		ppid = int32(pbi.InheritedFromUniqueProcessId)
	}

	// 缓存结果 - 带LRU访问时间记录
	ppidCacheMutex.Lock()
	ppidCache[pid] = ppid
	ppidAccessTimes[pid] = time.Now() // 记录访问时间
	ppidCacheMutex.Unlock()

	// 返回父进程ID
	return ppid
}

// enableSeDebugPrivilege 启用SeDebugPrivilege权限
func enableSeDebugPrivilege() error {
	var token windows.Token
	err := windows.OpenProcessToken(windows.CurrentProcess(), windows.TOKEN_ADJUST_PRIVILEGES|windows.TOKEN_QUERY, &token)
	if err != nil {
		return fmt.Errorf("OpenProcessToken failed: %v", err)
	}
	defer token.Close()

	// 获取SeDebugPrivilege的LUID
	var luid windows.LUID
	err = windows.LookupPrivilegeValue(nil, windows.StringToUTF16Ptr("SeDebugPrivilege"), &luid)
	if err != nil {
		return fmt.Errorf("LookupPrivilegeValue failed: %v", err)
	}

	// 使用windows包中的AdjustTokenPrivileges函数
	err = windows.AdjustTokenPrivileges(token, false, &windows.Tokenprivileges{
		PrivilegeCount: 1,
		Privileges: [1]windows.LUIDAndAttributes{
			{
				Luid:       luid,
				Attributes: windows.SE_PRIVILEGE_ENABLED,
			},
		},
	}, 0, nil, nil)

	if err != nil {
		return fmt.Errorf("AdjustTokenPrivileges failed: %v", err)
	}

	// 检查GetLastError，确保权限被正确分配
	lastError := windows.GetLastError()
	if lastError == windows.ERROR_NOT_ALL_ASSIGNED {
		return fmt.Errorf("SeDebugPrivilege not assigned to current user")
	}

	return nil
}

// init 初始化函数，尝试启用SeDebugPrivilege并初始化缓存
func init() {
	if err := enableSeDebugPrivilege(); err != nil {
		log.Printf("Warning: Failed to enable SeDebugPrivilege: %v", err)
		log.Printf("Some process information may not be available for system processes")
	} else {
		log.Printf("SeDebugPrivilege enabled successfully")
	}

	// 初始化系统总内存缓存
	initTotalMemoryCache()

	// 启动挂起缓存清理任务
	startSuspendCacheCleanup()

}

// handleProcessRequest 处理进程相关请求
func (cm *ConnectionManager) handleProcessRequest(packet *Packet) {
	switch packet.Header.Code {
	case ProcessList:
		cm.handleProcessList(packet)
	case ProcessKill:
		cm.handleProcessKill(packet)
	case ProcessStart:
		cm.handleProcessStart(packet)
	case ProcessDetails:
		cm.handleProcessDetails(packet)
	case ProcessSuspend:
		cm.handleProcessSuspend(packet)
	case ProcessResume:
		cm.handleProcessResume(packet)
	default:
		log.Printf("未知的进程操作代码: %d", packet.Header.Code)
	}
}

// handleProcessList 处理进程列表请求
func (cm *ConnectionManager) handleProcessList(packet *Packet) {
	log.Printf("🚀 开始处理进程列表请求")
	startTime := time.Now()

	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessListResponse{
		TaskID:    0,
		Success:   false,
		Processes: []ProcessFullInfo{},
		Error:     "",
		Count:     0,
	}

	var req ProcessListRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析进程列表请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendProcessResponse(ProcessList, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	processes, err := cm.getProcessList(req.ShowSystem)
	if err != nil {
		log.Printf("❌ 获取进程列表失败: %v", err)
		cm.sendProcessResponse(ProcessList, false, "获取进程列表失败", errorResp)
		return
	}

	elapsed := time.Since(startTime)
	log.Printf("✅ 成功获取 %d 个进程，耗时: %v (目标: <500ms)", len(processes), elapsed)

	// 性能警告
	if elapsed > 500*time.Millisecond {
		log.Printf("⚠️  性能警告: 处理时间 %v 超过目标 500ms", elapsed)
	} else {
		log.Printf("🎯 性能达标: 处理时间 %v", elapsed)
	}

	response := ProcessListResponse{
		TaskID:    req.TaskID,
		Success:   true,
		Processes: processes,
		Error:     "",
		Count:     len(processes),
	}

	cm.sendProcessResponse(ProcessList, true, fmt.Sprintf("获取进程列表成功，耗时: %v", elapsed), response)
}

// getProcessList 获取进程列表 - 高性能并发版本
func (cm *ConnectionManager) getProcessList(showSystem bool) ([]ProcessFullInfo, error) {
	startTime := time.Now()

	// 时间统计: 获取所有进程PID
	pidStart := time.Now()
	pids, err := process.Pids()
	pidTime := time.Since(pidStart)
	log.Printf("❗获取所有进程PID数量%d，耗时: %v", len(pids), pidTime)
	if err != nil {
		return nil, err
	}

	log.Printf("🚀 开始高性能并发处理 %d 个进程，工作池大小: %d", len(pids), WORKER_POOL_SIZE)

	// 限制最大处理数量，避免系统过载
	if len(pids) > MAX_PROCESSES_PER_REQUEST {
		pids = pids[:MAX_PROCESSES_PER_REQUEST]
		log.Printf("⚠️  进程数量过多，限制为前 %d 个进程", MAX_PROCESSES_PER_REQUEST)
	}

	// 并发处理相关变量 - 优化内存分配
	var (
		// 预分配切片容量，减少内存重新分配
		processes      = make([]ProcessFullInfo, 0, len(pids)/2) // 预估一半进程会被保留
		processesMutex sync.Mutex
		wg             sync.WaitGroup
		pidChan        = make(chan int32, WORKER_POOL_SIZE*2) // 缓冲区大小优化
		processedCount int32
		skippedCount   int32
	)

	// 启动worker goroutines
	for i := 0; i < WORKER_POOL_SIZE; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			// log.Printf("🔧 Worker %d 启动", workerID)

			for pid := range pidChan {
				// 时间统计: 单个进程处理
				processStart := time.Now()

				// 快速预过滤：只跳过最核心的系统进程（PID <= 4）
				if !showSystem && pid <= 4 {
					atomicAdd(&skippedCount, 1)
					continue
				}

				// 时间统计: 创建Process对象
				newProcessStart := time.Now()
				p, err := process.NewProcess(pid)
				if err != nil {
					atomicAdd(&skippedCount, 1)
					continue
				}
				newProcessTime := time.Since(newProcessStart)

				// 获取进程信息（优化版本）
				processInfo, err := cm.getProcessFullInfoOptimized(p, false) // false表示列表模式，跳过耗时操作
				if err != nil {
					atomicAdd(&skippedCount, 1)
					continue
				}

				// 过滤系统进程（二次过滤）- 只过滤真正的核心系统进程
				if !showSystem && processInfo.System {
					// 只过滤真正的系统进程，不基于PID范围
					atomicAdd(&skippedCount, 1)
					continue
				}

				// 线程安全地添加到结果集
				processesMutex.Lock()
				processes = append(processes, *processInfo)
				processesMutex.Unlock()

				atomicAdd(&processedCount, 1)

				// 时间统计输出（仅对耗时较长的进程）
				processTime := time.Since(processStart)
				if processTime > 20*time.Millisecond {
					log.Printf("⏱️  Worker %d PID %d 处理耗时: %v [NewProcess=%v]", workerID, pid, processTime, newProcessTime)
				}

				// 每处理50个进程输出一次进度
				if processedCount%50 == 0 {
					log.Printf("📊 Worker %d 进度: 已处理 %d 个进程", workerID, processedCount)
				}
			}

			// log.Printf("✅ Worker %d 完成", workerID)
		}(i)
	}

	// 批量发送PID到工作队列 - 优化内存使用
	go func() {
		const batchSize = 100 // 每批处理100个PID
		for i := 0; i < len(pids); i += batchSize {
			end := i + batchSize
			if end > len(pids) {
				end = len(pids)
			}
			// 分批发送PID
			for j := i; j < end; j++ {
				pidChan <- pids[j]
			}
			// 每批之间稍作停顿，避免系统过载
			if i+batchSize < len(pids) {
				time.Sleep(1 * time.Millisecond)
			}
		}
		close(pidChan)
	}()

	// 等待所有worker完成
	wg.Wait()

	elapsedTime := time.Since(startTime)
	log.Printf("🎉 并发处理完成！总耗时: %v, 处理: %d, 跳过: %d, 返回: %d",
		elapsedTime, processedCount, skippedCount, len(processes))

	return processes, nil
}

// atomicAdd 原子性地增加计数器 - 使用标准atomic包
func atomicAdd(counter *int32, delta int32) {
	atomic.AddInt32(counter, delta)
}

// getProcessFullInfoOptimized 获取单个进程信息 - 高性能并发优化版本
// 🚀 性能优化：使用WaitGroup和协程并行获取进程信息，提升处理速度
func (cm *ConnectionManager) getProcessFullInfoOptimized(p *process.Process, detailMode bool) (*ProcessFullInfo, error) {
	overallStart := time.Now()

	// 🚀 并发优化：使用WaitGroup协调多个协程并行获取进程信息
	var wg sync.WaitGroup
	var mu sync.Mutex // 保护时间统计变量

	// 进程信息结果变量
	var (
		name          string
		executable    string
		ppid          int32
		statusStr     string
		cpuPercent    float64
		memoryBytes   uint64
		memoryPercent float32
		priority      int32
		cmdline       string
		startTime     time.Time
		runTime       string
		username      string
		isSystem      bool
	)

	// 时间统计变量
	var (
		nameTime     time.Duration
		ppidTime     time.Duration
		statusTime   time.Duration
		cpuTime      time.Duration
		memTime      time.Duration
		priorityTime time.Duration
		cmdlineTime  time.Duration
		usernameTime time.Duration
		systemTime   time.Duration
	)

	// 协程1: 获取进程名称和可执行文件路径
	wg.Add(1)
	go func() {
		defer wg.Done()
		nameStart := time.Now()
		n, err := p.Name()
		var exe string
		if err != nil || n == "" {
			// 备选方案1：使用Windows API获取进程名
			if winName := cm.getWindowsProcessName(uint32(p.Pid)); winName != "" {
				n = winName
			} else {
				// 备选方案2：使用进程可执行文件路径
				if exePath, err := p.Exe(); err == nil && exePath != "" {
					n = filepath.Base(exePath)
					exe = exePath
				} else {
					// 最后备选：显示PID
					n = fmt.Sprintf("Process_%d", p.Pid)
				}
			}
		}
		// 获取可执行文件路径
		if exe == "" {
			if exePath, err := p.Exe(); err == nil {
				exe = exePath
			}
		}
		mu.Lock()
		name = n
		executable = exe
		nameTime = time.Since(nameStart)
		mu.Unlock()
	}()

	// 协程2: 获取父进程ID
	wg.Add(1)
	go func() {
		defer wg.Done()
		ppidStart := time.Now()
		parentPid := cm.getWindowsPPIDOptimized(uint32(p.Pid))
		mu.Lock()
		ppid = parentPid
		ppidTime = time.Since(ppidStart)
		mu.Unlock()
	}()

	// 协程3: 获取进程状态
	wg.Add(1)
	go func() {
		defer wg.Done()
		statusStart := time.Now()
		var s string
		status, err := p.Status()
		if err != nil || len(status) == 0 {
			s = getWindowsProcessStatus(p.Pid)
		} else {
			s = strings.Join(status, ",")
		}
		mu.Lock()
		statusStr = s
		statusTime = time.Since(statusStart)
		mu.Unlock()
	}()

	// 协程4: 获取CPU使用率
	wg.Add(1)
	go func() {
		defer wg.Done()
		cpuStart := time.Now()
		var cpu float64
		if detailMode {
			// 详情模式：获取精确CPU使用率
			var err error
			cpu, err = p.CPUPercent()
			if err != nil {
				cpu = 0.0
			} else {
				cpu2, err2 := p.CPUPercent()
				if err2 == nil {
					cpu = cpu2
				}
			}
		} else {
			// 列表模式：使用单次CPU计算
			var err error
			cpu, err = p.CPUPercent()
			if err != nil {
				cpu = 0.0
			}
		}

		// 🔧 修复CPU显示问题：将CPU使用率标准化到0-100%范围
		if cpu > 100.0 {
			numCPU := float64(runtime.NumCPU())
			if numCPU > 0 {
				cpu = cpu / numCPU
			}
			if cpu > 100.0 {
				cpu = 100.0
			}
		}

		mu.Lock()
		cpuPercent = cpu
		cpuTime = time.Since(cpuStart)
		mu.Unlock()
	}()

	// 协程5: 获取内存信息
	wg.Add(1)
	go func() {
		defer wg.Done()
		memStart := time.Now()
		var memBytes uint64
		var memPercent float32

		memInfo, err := p.MemoryInfo()
		if err != nil {
			memBytes = 0
			memPercent = 0.0
		} else {
			memBytes = memInfo.RSS
			totalMem := getTotalMemory()
			if totalMem > 0 {
				memPercent = float32(memInfo.RSS) / float32(totalMem) * 100
			}
		}

		mu.Lock()
		memoryBytes = memBytes
		memoryPercent = memPercent
		memTime = time.Since(memStart)
		mu.Unlock()
	}()

	// 协程6: 获取进程优先级
	wg.Add(1)
	go func() {
		defer wg.Done()
		priorityStart := time.Now()
		var prio int32
		if nice, err := p.Nice(); err == nil {
			prio = nice
		}
		mu.Lock()
		priority = prio
		priorityTime = time.Since(priorityStart)
		mu.Unlock()
	}()

	// 协程7: 获取用户名
	wg.Add(1)
	go func() {
		defer wg.Done()
		usernameStart := time.Now()
		u := cm.getOptimizedUsername(p.Pid)
		mu.Lock()
		username = u
		usernameTime = time.Since(usernameStart)
		mu.Unlock()
	}()

	// 协程8: 获取命令行和时间信息（仅在详情模式下）
	if detailMode {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 获取命令行
			if c, err := p.Cmdline(); err == nil {
				cmdline = c
				if len(cmdline) > 500 {
					cmdline = cmdline[:500] + "..."
				}
			}

			// 获取启动时间
			if createTime, err := p.CreateTime(); err == nil {
				startTime = time.Unix(createTime/1000, 0)
				runTime = time.Since(startTime).String()
			}
		}()
	} else {
		// 列表模式：跳过详细信息获取
		cmdline = ""
		startTime = time.Time{}
		runTime = ""
	}

	// 等待所有协程完成
	wg.Wait()

	// 协程9: 系统进程判断（需要等待name获取完成）
	systemStart := time.Now()
	isSystem = cm.isSystemProcessOptimized(p.Pid, name)
	systemTime = time.Since(systemStart)

	// 输出详细时间统计
	totalTime := time.Since(overallStart)
	sequentialTime := nameTime + ppidTime + statusTime + cpuTime + memTime + priorityTime + usernameTime + cmdlineTime + systemTime
	if detailMode || totalTime > 10*time.Millisecond {
		log.Printf("🚀 PID %d 并发优化耗时: 总计=%v, 串行模拟=%v, 提升=%.1f [名称=%v, PPID=%v, 状态=%v, CPU=%v, 内存=%v, 优先级=%v, 用户=%v, 命令行=%v, 系统=%v]",
			p.Pid, totalTime, sequentialTime, float64(sequentialTime-totalTime)/float64(sequentialTime)*100,
			nameTime, ppidTime, statusTime, cpuTime, memTime, priorityTime, usernameTime, cmdlineTime, systemTime)
	}

	// 从对象池获取ProcessFullInfo对象
	info := processInfoPool.Get().(*ProcessFullInfo)
	*info = ProcessFullInfo{
		PID:           p.Pid,
		PPID:          ppid,
		Name:          name,
		Executable:    executable,
		User:          username,
		Status:        statusStr,
		CPU:           cpuPercent,
		Memory:        memoryBytes,
		MemoryPercent: memoryPercent,
		Priority:      priority,
		StartTime:     startTime,
		RunTime:       runTime,
		Cmdline:       cmdline,
		System:        isSystem,
	}
	return info, nil
}

// getProcessFullInfo 获取单个进程信息 - 兼容性保留版本
// 为了保持向后兼容，保留原函数，内部调用优化版本
func (cm *ConnectionManager) getProcessFullInfo(p *process.Process) (*ProcessFullInfo, error) {
	pid := p.Pid

	// 🚀 性能优化：检查进程信息缓存
	processCacheMutex.RLock()
	if cachedInfo, exists := processInfoCache[pid]; exists {
		// 检查缓存是否过期（使用缓存创建时间而不是进程启动时间）
		if time.Since(cachedInfo.StartTime) < cacheExpiry {
			// 创建缓存信息的副本
			info := processInfoPool.Get().(*ProcessFullInfo)
			*info = *cachedInfo
			processCacheMutex.RUnlock()
			return info, nil
		}
	}
	processCacheMutex.RUnlock()

	// 缓存未命中或已过期，获取新的进程信息
	info, err := cm.getProcessFullInfoOptimized(p, true)
	if err != nil {
		return nil, err
	}

	// 🚀 性能优化：将结果缓存（使用当前时间作为缓存时间戳）
	processCacheMutex.Lock()
	// 创建缓存副本，避免修改原始数据
	cachedInfo := *info
	cachedInfo.StartTime = time.Now() // 使用当前时间作为缓存时间戳
	processInfoCache[pid] = &cachedInfo
	processCacheMutex.Unlock()

	return info, nil
}

// releaseProcessInfo 释放ProcessFullInfo对象回对象池
// 使用完ProcessFullInfo对象后应调用此函数释放资源
func releaseProcessInfo(info *ProcessFullInfo) {
	if info != nil {
		// 清理敏感数据
		info.Cmdline = ""
		info.User = ""
		// 将对象放回池中
		processInfoPool.Put(info)
	}
}

// getOptimizedUsername 获取优化的用户名（使用缓存）
func (cm *ConnectionManager) getOptimizedUsername(pid int32) string {
	// 时间统计: 缓存查找
	cacheStart := time.Now()
	sidKey := fmt.Sprintf("pid_%d", pid)
	if username, exists := getCachedUsername(sidKey); exists {
		cacheTime := time.Since(cacheStart)
		if cacheTime > 1*time.Millisecond {
			log.Printf("⏱️  PID %d 用户名缓存命中，耗时: %v", pid, cacheTime)
		}
		return username
	}
	cacheTime := time.Since(cacheStart)

	// 时间统计: 获取用户名
	userStart := time.Now()
	var username string

	// 优化策略：直接使用Windows API作为主要方法，避免gopsutil开销
	winApiStart := time.Now()
	username = cm.getWindowsUsernameOptimized(uint32(pid))
	winApiTime := time.Since(winApiStart)

	// 备选方法：如果Windows API失败，尝试gopsutil（但这种情况应该很少）
	gopsutilStart := time.Now()
	var gopsutilTime time.Duration
	if username == "" {
		p, err := process.NewProcess(pid)
		if err == nil {
			if gopsutilUsername, err := p.Username(); err == nil && gopsutilUsername != "" {
				username = gopsutilUsername
			}
		}
		gopsutilTime = time.Since(gopsutilStart)
	}

	// 最后的备选：如果都失败了，显示UID
	if username == "" {
		username = fmt.Sprintf("UID:%d", pid)
	}

	// 存入缓存
	setCachedUsername(sidKey, username)
	userTime := time.Since(userStart)

	// 输出详细时间统计（仅在耗时较长时）
	if userTime > 5*time.Millisecond {
		log.Printf("⏱️  PID %d 用户名获取耗时: 总计=%v [缓存=%v, WinAPI=%v, gopsutil=%v]",
			pid, userTime, cacheTime, winApiTime, gopsutilTime)
	}

	return username
}

// isSystemProcessOptimized 优化的系统进程判断
func (cm *ConnectionManager) isSystemProcessOptimized(pid int32, name string) bool {
	// 快速判断：基于PID范围（只有最核心的系统进程）
	if pid <= 4 {
		return true
	}

	// 基于进程名判断（更严格的系统进程列表）
	systemProcessNames := []string{
		"System", "Registry", "smss.exe", "csrss.exe", "wininit.exe",
		"winlogon.exe", "services.exe", "lsass.exe",
	}

	for _, sysName := range systemProcessNames {
		if strings.EqualFold(name, sysName) {
			return true
		}
	}

	// 特殊判断：svchost.exe 只有在特定条件下才算系统进程
	if strings.EqualFold(name, "svchost.exe") && pid < 1000 {
		return true
	}

	return false
}

// getWindowsUsernameOptimized 获取Windows进程的用户名 - 高性能优化版本
func (cm *ConnectionManager) getWindowsUsernameOptimized(pid uint32) string {
	// 快速判断：系统关键进程
	if pid <= 4 {
		return "SYSTEM"
	}

	// 时间统计: SID缓存查找
	sidCacheStart := time.Now()
	sidKey := fmt.Sprintf("sid_%d", pid)
	if username, exists := getCachedUsername(sidKey); exists {
		sidCacheTime := time.Since(sidCacheStart)
		if sidCacheTime > 1*time.Millisecond {
			log.Printf("⏱️  PID %d SID缓存命中，耗时: %v", pid, sidCacheTime)
		}
		if username == "__ERROR__" {
			return "" // 缓存的错误结果
		}
		return username
	}
	sidCacheTime := time.Since(sidCacheStart)

	// 时间统计: Windows API调用
	winApiStart := time.Now()
	resultChan := make(chan string, 1)
	go func() {
		username := cm.getWindowsUsernameInternal(pid)
		resultChan <- username
	}()

	// 设置超时时间为100ms
	select {
	case username := <-resultChan:
		winApiTime := time.Since(winApiStart)
		if winApiTime > 10*time.Millisecond {
			log.Printf("⏱️  PID %d Windows API用户名获取耗时: %v [SID缓存=%v]", pid, winApiTime, sidCacheTime)
		}
		if username == "" {
			// 缓存错误结果，避免重复尝试
			setCachedUsername(sidKey, "__ERROR__")
		} else {
			// 缓存成功结果
			setCachedUsername(sidKey, username)
		}
		return username
	case <-time.After(100 * time.Millisecond):
		winApiTime := time.Since(winApiStart)
		log.Printf("⚠️  PID %d Windows API用户名获取超时: %v [SID缓存=%v]", pid, winApiTime, sidCacheTime)
		// 超时，缓存错误结果
		setCachedUsername(sidKey, "__ERROR__")
		return ""
	}
}

// getWindowsProcessName 使用Windows API获取进程名称
func (cm *ConnectionManager) getWindowsProcessName(pid uint32) string {
	// 尝试打开进程句柄
	handle, err := windows.OpenProcess(windows.PROCESS_QUERY_LIMITED_INFORMATION, false, pid)
	if err != nil {
		return ""
	}
	defer windows.CloseHandle(handle)

	// 获取进程映像文件名
	var buffer [windows.MAX_PATH]uint16
	size := uint32(len(buffer))
	err = windows.QueryFullProcessImageName(handle, 0, &buffer[0], &size)
	if err != nil {
		return ""
	}

	// 转换为字符串并提取文件名
	fullPath := windows.UTF16ToString(buffer[:size])
	if fullPath != "" {
		return filepath.Base(fullPath)
	}

	return ""
}

// getWindowsUsernameInternal 内部用户名获取函数
func (cm *ConnectionManager) getWindowsUsernameInternal(pid uint32) string {
	handle, err := windows.OpenProcess(windows.PROCESS_QUERY_INFORMATION, false, pid)
	if err != nil {
		return ""
	}
	defer windows.CloseHandle(handle)

	var token windows.Token
	err = windows.OpenProcessToken(handle, windows.TOKEN_QUERY, &token)
	if err != nil {
		return ""
	}
	defer token.Close()

	tokenUser, err := token.GetTokenUser()
	if err != nil {
		return ""
	}

	// 尝试从SID缓存获取用户名
	sidStr := tokenUser.User.Sid.String()
	if username, exists := getCachedUsername(sidStr); exists {
		return username
	}

	account, domain, _, err := tokenUser.User.Sid.LookupAccount("")
	if err != nil {
		return ""
	}

	var username string
	if domain != "" {
		username = domain + "\\" + account
	} else {
		username = account
	}

	// 缓存SID->用户名映射
	setCachedUsername(sidStr, username)
	return username
}

// sendProcessResponse 发送进程操作响应
func (cm *ConnectionManager) sendProcessResponse(code uint8, success bool, message string, data interface{}) {
	// 对于进程列表请求，需要特殊处理响应格式
	if code == ProcessList {
		if listResp, ok := data.(ProcessListResponse); ok {
			// 构造服务器端期望的ProcessListResponse格式，包含TaskID
			response := map[string]interface{}{
				"task_id":   listResp.TaskID,
				"success":   success,
				"processes": listResp.Processes,
				"error":     "",
				"count":     listResp.Count,
			}
			if !success {
				response["error"] = message
				response["processes"] = []ProcessFullInfo{}
				response["count"] = 0
			}
			cm.sendResp(Process, code, response)
			return
		}
	}

	// 对于进程终止请求，需要特殊处理响应格式
	if code == ProcessKill {
		if killResp, ok := data.(ProcessKillResponse); ok {
			// 直接发送ProcessKillResponse结构体
			cm.sendResp(Process, code, killResp)
			return
		}
	}

	// 对于进程启动请求，需要特殊处理响应格式
	if code == ProcessStart {
		if startResp, ok := data.(ProcessStartResponse); ok {
			// 直接发送ProcessStartResponse结构体
			cm.sendResp(Process, code, startResp)
			return
		}
	}

	// 对于进程详情请求，需要特殊处理响应格式
	if code == ProcessDetails {
		if detailsResp, ok := data.(ProcessDetailsResponse); ok {
			// 直接发送ProcessDetailsResponse结构体
			cm.sendResp(Process, code, detailsResp)
			return
		}
	}

	// 对于进程挂起请求，需要特殊处理响应格式
	if code == ProcessSuspend {
		if suspendResp, ok := data.(ProcessSuspendResponse); ok {
			// 直接发送ProcessSuspendResponse结构体
			cm.sendResp(Process, code, suspendResp)
			return
		}
	}

	// 对于进程恢复请求，需要特殊处理响应格式
	if code == ProcessResume {
		if resumeResp, ok := data.(ProcessResumeResponse); ok {
			// 直接发送ProcessResumeResponse结构体
			cm.sendResp(Process, code, resumeResp)
			return
		}
	}
}

// getWindowsProcessStatus 使用Windows API获取进程状态 - 性能优化版本
func getWindowsProcessStatus(pid int32) string {
	// 尝试打开进程句柄
	handle, err := windows.OpenProcess(windows.PROCESS_QUERY_LIMITED_INFORMATION, false, uint32(pid))
	if err != nil {
		// 无法打开进程句柄，可能进程已退出或权限不足
		return "unknown"
	}
	defer windows.CloseHandle(handle)

	// 获取进程退出代码
	var exitCode uint32
	err = windows.GetExitCodeProcess(handle, &exitCode)
	if err != nil {
		return "unknown"
	}

	// 如果退出代码不是STILL_ACTIVE(259)，说明进程已停止
	if exitCode != STILL_ACTIVE {
		return "Stopped"
	}

	// 🚀 性能优化：只对最近被挂起的进程进行详细的挂起状态检查
	// 检查进程是否在挂起缓存中
	suspendedMutex.RLock()
	suspendTime, isSuspended := suspendedProcesses[pid]
	suspendedMutex.RUnlock()

	if isSuspended {
		// 检查缓存是否过期
		if time.Since(suspendTime) > suspendCacheExpiry {
			// 缓存过期，清理并使用快速检查
			suspendedMutex.Lock()
			delete(suspendedProcesses, pid)
			suspendedMutex.Unlock()
			return "Running" // 默认为运行状态
		}

		// 进程在挂起缓存中且未过期，进行详细的挂起状态检查
		return getDetailedSuspendStatus(pid)
	}

	// 进程不在挂起缓存中，直接返回运行状态（避免耗时的线程检查）
	return "Running"
}

// getDetailedSuspendStatus 获取详细的挂起状态（仅对可能挂起的进程调用）
func getDetailedSuspendStatus(pid int32) string {
	// 尝试打开进程句柄，包含挂起权限
	handle, err := windows.OpenProcess(windows.PROCESS_QUERY_LIMITED_INFORMATION|windows.PROCESS_SUSPEND_RESUME, false, uint32(pid))
	if err != nil {
		return "Running"
	}
	defer windows.CloseHandle(handle)

	// 检查线程状态来判断是否挂起
	// 打开进程的第一个线程
	threadHandle, err := getFirstThreadHandle(uint32(pid))
	if err != nil {
		return "Running"
	}
	defer windows.CloseHandle(threadHandle)

	// 检查线程挂起计数
	suspendCount := getSuspendCount(threadHandle)
	if suspendCount > 0 {
		return "Suspended"
	}

	// 如果检查结果不是挂起状态，从缓存中移除
	suspendedMutex.Lock()
	delete(suspendedProcesses, pid)
	suspendedMutex.Unlock()

	return "Running"
}

// getFirstThreadHandle 获取进程的第一个线程句柄
func getFirstThreadHandle(pid uint32) (windows.Handle, error) {
	snapshot, err := windows.CreateToolhelp32Snapshot(windows.TH32CS_SNAPTHREAD, 0)
	if err != nil {
		return 0, err
	}
	defer windows.CloseHandle(snapshot)

	var te windows.ThreadEntry32
	te.Size = uint32(unsafe.Sizeof(te))

	err = windows.Thread32First(snapshot, &te)
	if err != nil {
		return 0, err
	}

	for {
		if te.OwnerProcessID == pid {
			// 找到属于目标进程的线程
			threadHandle, err := windows.OpenThread(windows.THREAD_QUERY_INFORMATION|windows.THREAD_SUSPEND_RESUME, false, te.ThreadID)
			if err == nil {
				return threadHandle, nil
			}
		}

		err = windows.Thread32Next(snapshot, &te)
		if err != nil {
			break
		}
	}

	return 0, fmt.Errorf("no thread found for process %d", pid)
}

// getSuspendCount 获取线程的挂起计数
func getSuspendCount(threadHandle windows.Handle) uint32 {
	// 使用ResumeThread来检查挂起计数
	// ResumeThread返回之前的挂起计数
	prevCount, err := windows.ResumeThread(threadHandle)
	if err != nil {
		log.Println("getSuspendCount error :", err)
	}
	if prevCount > 0 {
		// 如果之前有挂起计数，需要重新挂起
		// 使用ntdll.dll的NtSuspendThread
		ntdll := windows.NewLazySystemDLL("ntdll.dll")
		ntSuspendThread := ntdll.NewProc("NtSuspendThread")
		ntSuspendThread.Call(uintptr(threadHandle), 0)
		return prevCount
	}

	return 0
}

// cleanupExpiredSuspendCache 清理过期的挂起进程缓存
func cleanupExpiredSuspendCache() {
	suspendedMutex.Lock()
	defer suspendedMutex.Unlock()

	now := time.Now()
	for pid, suspendTime := range suspendedProcesses {
		if now.Sub(suspendTime) > suspendCacheExpiry {
			delete(suspendedProcesses, pid)
		}
	}
}

// startSuspendCacheCleanup 启动挂起缓存清理任务
func startSuspendCacheCleanup() {
	go func() {
		ticker := time.NewTicker(CACHE_CLEANUP_INTERVAL)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				cleanupExpiredSuspendCache()
			}
		}
	}()
}
