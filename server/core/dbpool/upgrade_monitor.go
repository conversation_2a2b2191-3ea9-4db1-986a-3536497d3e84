package dbpool

import (
	"encoding/json"
	"server/global"
	"sync"
	"time"

	"go.uber.org/zap"
)

// UpgradeMonitor 数据库连接池升级监控器
type UpgradeMonitor struct {
	startTime      time.Time
	upgradeStats   map[string]*UpgradeStats
	mutex          sync.RWMutex
	totalOperations int64
	poolOperations  int64
}

// UpgradeStats 升级统计信息
type UpgradeStats struct {
	ModuleName       string    `json:"module_name"`
	TotalOperations  int64     `json:"total_operations"`
	PoolOperations   int64     `json:"pool_operations"`
	DirectOperations int64     `json:"direct_operations"`
	UpgradeRate      float64   `json:"upgrade_rate"`
	LastUpdated      time.Time `json:"last_updated"`
}

// GlobalUpgradeMonitor 全局升级监控器
var GlobalUpgradeMonitor *UpgradeMonitor

// init 初始化全局升级监控器
func init() {
	GlobalUpgradeMonitor = NewUpgradeMonitor()
}

// NewUpgradeMonitor 创建新的升级监控器
func NewUpgradeMonitor() *UpgradeMonitor {
	return &UpgradeMonitor{
		startTime:    time.Now(),
		upgradeStats: make(map[string]*UpgradeStats),
	}
}

// RecordPoolOperation 记录连接池操作
func (um *UpgradeMonitor) RecordPoolOperation(moduleName string) {
	um.mutex.Lock()
	defer um.mutex.Unlock()

	if stats, exists := um.upgradeStats[moduleName]; exists {
		stats.PoolOperations++
		stats.TotalOperations++
		stats.UpgradeRate = float64(stats.PoolOperations) / float64(stats.TotalOperations) * 100
		stats.LastUpdated = time.Now()
	} else {
		um.upgradeStats[moduleName] = &UpgradeStats{
			ModuleName:      moduleName,
			PoolOperations:  1,
			TotalOperations: 1,
			UpgradeRate:     100.0,
			LastUpdated:     time.Now(),
		}
	}

	um.poolOperations++
	um.totalOperations++
}

// RecordDirectOperation 记录直接数据库操作
func (um *UpgradeMonitor) RecordDirectOperation(moduleName string) {
	um.mutex.Lock()
	defer um.mutex.Unlock()

	if stats, exists := um.upgradeStats[moduleName]; exists {
		stats.DirectOperations++
		stats.TotalOperations++
		stats.UpgradeRate = float64(stats.PoolOperations) / float64(stats.TotalOperations) * 100
		stats.LastUpdated = time.Now()
	} else {
		um.upgradeStats[moduleName] = &UpgradeStats{
			ModuleName:       moduleName,
			DirectOperations: 1,
			TotalOperations:  1,
			UpgradeRate:      0.0,
			LastUpdated:      time.Now(),
		}
	}

	um.totalOperations++
}

// GetUpgradeReport 获取升级报告
func (um *UpgradeMonitor) GetUpgradeReport() map[string]interface{} {
	um.mutex.RLock()
	defer um.mutex.RUnlock()

	overallUpgradeRate := float64(0)
	if um.totalOperations > 0 {
		overallUpgradeRate = float64(um.poolOperations) / float64(um.totalOperations) * 100
	}

	moduleStats := make([]UpgradeStats, 0, len(um.upgradeStats))
	for _, stats := range um.upgradeStats {
		moduleStats = append(moduleStats, *stats)
	}

	return map[string]interface{}{
		"start_time":           um.startTime,
		"current_time":         time.Now(),
		"total_operations":     um.totalOperations,
		"pool_operations":      um.poolOperations,
		"direct_operations":    um.totalOperations - um.poolOperations,
		"overall_upgrade_rate": overallUpgradeRate,
		"module_stats":         moduleStats,
		"db_pool_stats":        GetGlobalDBPoolStatsCompatible(),
	}
}

// PrintUpgradeReport 打印升级报告
func (um *UpgradeMonitor) PrintUpgradeReport() {
	report := um.GetUpgradeReport()
	
	global.LOG.Info("🚀 数据库连接池升级报告",
		zap.Time("start_time", um.startTime),
		zap.Int64("total_operations", um.totalOperations),
		zap.Int64("pool_operations", um.poolOperations),
		zap.Float64("overall_upgrade_rate", report["overall_upgrade_rate"].(float64)))

	// 打印模块统计
	for _, stats := range report["module_stats"].([]UpgradeStats) {
		global.LOG.Info("模块升级统计",
			zap.String("module", stats.ModuleName),
			zap.Int64("total", stats.TotalOperations),
			zap.Int64("pool", stats.PoolOperations),
			zap.Int64("direct", stats.DirectOperations),
			zap.Float64("upgrade_rate", stats.UpgradeRate))
	}
}

// GetUpgradeReportJSON 获取JSON格式的升级报告
func (um *UpgradeMonitor) GetUpgradeReportJSON() (string, error) {
	report := um.GetUpgradeReport()
	jsonData, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

// StartPeriodicReport 启动定期报告
func (um *UpgradeMonitor) StartPeriodicReport(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			um.PrintUpgradeReport()
		}
	}()
}

// GetModuleUpgradeStatus 获取模块升级状态
func (um *UpgradeMonitor) GetModuleUpgradeStatus(moduleName string) *UpgradeStats {
	um.mutex.RLock()
	defer um.mutex.RUnlock()

	if stats, exists := um.upgradeStats[moduleName]; exists {
		return stats
	}
	return nil
}

// IsModuleFullyUpgraded 检查模块是否完全升级
func (um *UpgradeMonitor) IsModuleFullyUpgraded(moduleName string) bool {
	stats := um.GetModuleUpgradeStatus(moduleName)
	return stats != nil && stats.UpgradeRate >= 100.0
}

// GetUpgradeProgress 获取整体升级进度
func (um *UpgradeMonitor) GetUpgradeProgress() float64 {
	um.mutex.RLock()
	defer um.mutex.RUnlock()

	if um.totalOperations == 0 {
		return 0.0
	}
	return float64(um.poolOperations) / float64(um.totalOperations) * 100
}

// LogUpgradeMetrics 记录升级指标到日志
func (um *UpgradeMonitor) LogUpgradeMetrics() {
	progress := um.GetUpgradeProgress()
	dbStats := GetGlobalDBPoolStatsCompatible()
	
	global.LOG.Info("📊 数据库连接池升级指标",
		zap.Float64("upgrade_progress", progress),
		zap.Int64("total_operations", um.totalOperations),
		zap.Int64("pool_operations", um.poolOperations),
		zap.Any("connection_stats", map[string]interface{}{
			"max_open_conns": dbStats["max_open_conns"],
			"open_conns":     dbStats["open_conns"],
			"in_use_conns":   dbStats["in_use_conns"],
			"idle_conns":     dbStats["idle_conns"],
		}),
		zap.Any("performance_stats", map[string]interface{}{
			"total_queries":    dbStats["total_queries"],
			"success_queries":  dbStats["success_queries"],
			"failed_queries":   dbStats["failed_queries"],
			"slow_queries":     dbStats["slow_queries"],
			"success_rate":     dbStats["success_rate"],
		}))
}

// 便捷函数

// RecordPoolOp 记录连接池操作的便捷函数
func RecordPoolOp(moduleName string) {
	if GlobalUpgradeMonitor != nil {
		GlobalUpgradeMonitor.RecordPoolOperation(moduleName)
	}
}

// RecordDirectOp 记录直接操作的便捷函数
func RecordDirectOp(moduleName string) {
	if GlobalUpgradeMonitor != nil {
		GlobalUpgradeMonitor.RecordDirectOperation(moduleName)
	}
}

// PrintUpgradeStatus 打印升级状态的便捷函数
func PrintUpgradeStatus() {
	if GlobalUpgradeMonitor != nil {
		GlobalUpgradeMonitor.PrintUpgradeReport()
	}
}

// GetUpgradeProgressPercent 获取升级进度百分比的便捷函数
func GetUpgradeProgressPercent() float64 {
	if GlobalUpgradeMonitor != nil {
		return GlobalUpgradeMonitor.GetUpgradeProgress()
	}
	return 0.0
}

// StartUpgradeMonitoring 启动升级监控
func StartUpgradeMonitoring() {
	if GlobalUpgradeMonitor != nil {
		// 每5分钟打印一次升级报告
		GlobalUpgradeMonitor.StartPeriodicReport(5 * time.Minute)
		global.LOG.Info("🚀 数据库连接池升级监控已启动")
	}
}
