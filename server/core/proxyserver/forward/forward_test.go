package forward

import (
	"net"
	"server/global"
	"server/model/basic"
	"testing"
	"time"

	"go.uber.org/zap"
)

func TestForwardProxyServer_StartStop(t *testing.T) {
	// 初始化logger避免nil指针
	if global.LOG == nil {
		global.LOG = zap.NewNop()
	}

	// 跳过需要数据库的测试
	t.<PERSON><PERSON>("跳过需要数据库连接的测试")

	// 创建测试代理配置
	proxy := &basic.Proxy{
		ProxyID:      "test-forward-proxy",
		Name:         "测试正向代理",
		Type:         "forward",
		UserPort:     18090,
		ClientPort:   18091,
		ClientID:     1,
		AuthRequired: false,
		Username:     "",
		Password:     "",
		AllowedIPs:   "",
		BlockedIPs:   "",
	}

	// 启动服务
	err := GlobalForwardManager.StartServer(proxy)
	if err != nil {
		t.Fatalf("启动正向代理失败: %v", err)
	}
	defer GlobalForwardManager.StopServer(proxy)

	// 等待服务启动
	time.Sleep(100 * time.Millisecond)

	// 验证服务是否在运行
	GlobalForwardManager.lock.Lock()
	server, exists := GlobalForwardManager.servers[proxy]
	GlobalForwardManager.lock.Unlock()

	if !exists {
		t.Fatal("服务器未在管理器中找到")
	}

	if !server.running {
		t.Fatal("服务器未运行")
	}

	// 验证端口是否被监听
	conn, err := net.DialTimeout("tcp", "localhost:18090", time.Second)
	if err == nil {
		conn.Close()
		// 连接成功说明端口在监听（虽然会因为没有真实的client而失败）
	}

	// 停止服务
	err = GlobalForwardManager.StopServer(proxy)
	if err != nil {
		t.Fatalf("停止正向代理失败: %v", err)
	}

	// 验证服务已停止
	GlobalForwardManager.lock.Lock()
	_, exists = GlobalForwardManager.servers[proxy]
	GlobalForwardManager.lock.Unlock()

	if exists {
		t.Fatal("服务器仍在管理器中")
	}
}

func TestForwardManager_StartStopServer(t *testing.T) {
	// 初始化logger避免nil指针
	if global.LOG == nil {
		global.LOG = zap.NewNop()
	}

	// 跳过需要数据库的测试
	t.Skip("跳过需要数据库连接的测试")

	// 创建测试代理配置
	proxy := &basic.Proxy{
		ProxyID:      "test-forward-manager",
		Name:         "测试正向代理管理器",
		Type:         "forward",
		UserPort:     18092,
		ClientPort:   18093,
		ClientID:     2,
		AuthRequired: false,
		Username:     "",
		Password:     "",
		AllowedIPs:   "",
		BlockedIPs:   "",
	}

	// 测试启动
	err := GlobalForwardManager.StartServer(proxy)
	if err != nil {
		t.Fatalf("启动服务失败: %v", err)
	}

	// 验证服务存在
	GlobalForwardManager.lock.Lock()
	_, exists := GlobalForwardManager.servers[proxy]
	GlobalForwardManager.lock.Unlock()

	if !exists {
		t.Fatal("服务未添加到管理器")
	}

	// 测试停止
	err = GlobalForwardManager.StopServer(proxy)
	if err != nil {
		t.Fatalf("停止服务失败: %v", err)
	}

	// 验证服务已移除
	GlobalForwardManager.lock.Lock()
	_, exists = GlobalForwardManager.servers[proxy]
	GlobalForwardManager.lock.Unlock()

	if exists {
		t.Fatal("服务未从管理器中移除")
	}
}

func TestForwardAccessController(t *testing.T) {
	// 创建测试配置
	proxy := &basic.Proxy{
		AllowedIPs: "192.168.1.*,**********",
		BlockedIPs: "*************",
	}
	
	config := NewForwardProxyConfig(proxy)
	controller := NewForwardAccessController(config)
	
	// 测试允许的IP
	if !controller.IsAllowed("************") {
		t.Error("应该允许************")
	}
	
	if !controller.IsAllowed("**********") {
		t.Error("应该允许**********")
	}
	
	// 测试被阻止的IP
	if controller.IsAllowed("*************") {
		t.Error("应该阻止*************")
	}
	
	// 测试不在白名单中的IP
	if controller.IsAllowed("**********") {
		t.Error("应该阻止**********（不在白名单中）")
	}
}

func TestForwardProxyStats(t *testing.T) {
	// 初始化logger避免nil指针
	if global.LOG == nil {
		global.LOG = zap.NewNop()
	}
	
	proxy := &basic.Proxy{
		ProxyID: "test-stats",
	}
	
	stats := NewForwardProxyStats(proxy)
	defer stats.stopPeriodicUpdate()
	
	// 测试连接统计
	stats.AddConnection()
	stats.AddConnection()
	
	// 测试流量统计
	stats.AddBytes(1024, 2048)
	stats.AddBytes(512, 1024)
	
	// 获取统计信息
	totalConn, activeConn, bytesSent, bytesReceived, _ := stats.GetStats()
	
	if totalConn != 2 {
		t.Errorf("期望总连接数为2，实际为%d", totalConn)
	}
	
	if activeConn != 2 {
		t.Errorf("期望活跃连接数为2，实际为%d", activeConn)
	}
	
	if bytesSent != 1536 {
		t.Errorf("期望发送字节数为1536，实际为%d", bytesSent)
	}
	
	if bytesReceived != 3072 {
		t.Errorf("期望接收字节数为3072，实际为%d", bytesReceived)
	}
	
	// 测试移除连接
	stats.RemoveConnection()
	_, activeConn, _, _, _ = stats.GetStats()
	
	if activeConn != 1 {
		t.Errorf("期望活跃连接数为1，实际为%d", activeConn)
	}
}

func TestPortReuse(t *testing.T) {
	// 初始化logger避免nil指针
	if global.LOG == nil {
		global.LOG = zap.NewNop()
	}

	// 跳过需要数据库的测试
	t.Skip("跳过需要数据库连接的测试")

	// 测试端口复用检查
	port := uint16(18094)
	
	if !GlobalForwardManager.CanReusePort(port) {
		t.Error("空端口应该可以复用")
	}
	
	// 创建测试代理
	proxy1 := &basic.Proxy{
		ProxyID:    "test-reuse-1",
		Type:       "forward",
		UserPort:   port,
		ClientPort: 18095,
		ClientID:   3,
	}
	
	proxy2 := &basic.Proxy{
		ProxyID:    "test-reuse-2", 
		Type:       "forward",
		UserPort:   port,
		ClientPort: 18096,
		ClientID:   4,
	}
	
	// 启动第一个代理
	err := GlobalForwardManager.StartServerWithPortReuse(proxy1, true)
	if err != nil {
		t.Fatalf("启动第一个代理失败: %v", err)
	}
	defer GlobalForwardManager.StopServer(proxy1)
	
	// 检查端口复用信息
	info := GlobalForwardManager.GetPortReuseInfo(port)
	if len(info) != 1 {
		t.Errorf("期望端口复用信息数量为1，实际为%d", len(info))
	}
	
	// 启动第二个代理（端口复用）
	err = GlobalForwardManager.StartServerWithPortReuse(proxy2, true)
	if err != nil {
		t.Fatalf("启动第二个代理失败: %v", err)
	}
	defer GlobalForwardManager.StopServer(proxy2)
	
	// 检查端口复用信息
	info = GlobalForwardManager.GetPortReuseInfo(port)
	if len(info) != 2 {
		t.Errorf("期望端口复用信息数量为2，实际为%d", len(info))
	}
}
