package socks5

import (
	"net"
	"server/global"
	"server/model/basic"
	"testing"
	"time"

	"go.uber.org/zap"
)

func TestSocks5ReverseProxyServer_StartStop(t *testing.T) {
	// 初始化logger避免nil指针
	if global.LOG == nil {
		global.LOG = zap.NewNop()
	}

	// 创建测试代理配置
	proxy := &basic.Proxy{
		UserPort:   18080,
		ClientPort: 18081,
		Username:   "",
		Password:   "",
	}

	// 使用管理器创建服务器实例（这样会正确初始化所有组件）
	err := GlobalSocks5Manager.StartServer(proxy)
	if err != nil {
		t.Fatalf("启动服务失败: %v", err)
	}

	// 验证服务器已正确创建
	GlobalSocks5Manager.lock.Lock()
	_, exists := GlobalSocks5Manager.servers[proxy]
	GlobalSocks5Manager.lock.Unlock()

	if !exists {
		t.Fatal("服务器未正确创建")
	}

	// 等待服务启动
	time.Sleep(100 * time.Millisecond)

	// 测试端口是否可连接
	userConn, err := net.DialTimeout("tcp", "localhost:18080", time.Second)
	if err != nil {
		t.Fatalf("连接用户端口失败: %v", err)
	}
	userConn.Close()

	clientConn, err := net.DialTimeout("tcp", "localhost:18081", time.Second)
	if err != nil {
		t.Fatalf("连接客户端端口失败: %v", err)
	}
	clientConn.Close()

	// 测试停止
	err = GlobalSocks5Manager.StopServer(proxy)
	if err != nil {
		t.Fatalf("停止服务失败: %v", err)
	}

	// 等待服务停止
	time.Sleep(100 * time.Millisecond)

	// 验证端口已关闭
	_, err = net.DialTimeout("tcp", "localhost:18080", 100*time.Millisecond)
	if err == nil {
		t.Fatal("用户端口应该已关闭")
	}

	_, err = net.DialTimeout("tcp", "localhost:18081", 100*time.Millisecond)
	if err == nil {
		t.Fatal("客户端端口应该已关闭")
	}
}

func TestSocks5Manager_StartStopServer(t *testing.T) {
	// 创建测试代理配置
	proxy := &basic.Proxy{
		UserPort:   18082,
		ClientPort: 18083,
		Username:   "",
		Password:   "",
	}

	// 测试启动
	err := GlobalSocks5Manager.StartServer(proxy)
	if err != nil {
		t.Fatalf("启动管理器服务失败: %v", err)
	}

	// 等待服务启动
	time.Sleep(100 * time.Millisecond)

	// 验证服务已添加到管理器
	GlobalSocks5Manager.lock.Lock()
	_, exists := GlobalSocks5Manager.servers[proxy]
	GlobalSocks5Manager.lock.Unlock()

	if !exists {
		t.Fatal("服务未添加到管理器")
	}

	// 测试停止
	err = GlobalSocks5Manager.StopServer(proxy)
	if err != nil {
		t.Fatalf("停止管理器服务失败: %v", err)
	}

	// 验证服务已从管理器移除
	GlobalSocks5Manager.lock.Lock()
	_, exists = GlobalSocks5Manager.servers[proxy]
	GlobalSocks5Manager.lock.Unlock()

	if exists {
		t.Fatal("服务未从管理器移除")
	}
}

func TestSocks5Authentication(t *testing.T) {
	// 初始化logger避免nil指针
	if global.LOG == nil {
		global.LOG = zap.NewNop()
	}

	// 创建需要认证的代理配置
	proxy := &basic.Proxy{
		UserPort:     18084,
		ClientPort:   18085,
		AuthRequired: true,
		Username:     "testuser",
		Password:     "testpass",
	}

	// 启动服务
	err := GlobalSocks5Manager.StartServer(proxy)
	if err != nil {
		t.Fatalf("启动认证服务失败: %v", err)
	}
	defer GlobalSocks5Manager.StopServer(proxy)

	// 等待服务启动
	time.Sleep(100 * time.Millisecond)

	// 测试连接（这里只测试连接建立，不测试完整的SOCKS5握手）
	conn, err := net.DialTimeout("tcp", "localhost:18084", time.Second)
	if err != nil {
		t.Fatalf("连接认证服务失败: %v", err)
	}
	defer conn.Close()

	// 发送SOCKS5版本协商
	_, err = conn.Write([]byte{0x05, 0x02, 0x00, 0x02}) // 支持无认证和用户名密码认证
	if err != nil {
		t.Fatalf("发送版本协商失败: %v", err)
	}

	// 读取响应
	response := make([]byte, 2)
	_, err = conn.Read(response)
	if err != nil {
		t.Fatalf("读取版本协商响应失败: %v", err)
	}

	// 验证选择了用户名密码认证
	if response[0] != 0x05 || response[1] != 0x02 {
		t.Fatalf("期望选择用户名密码认证，实际: %v", response)
	}

	// 发送用户名密码认证数据
	username := "testuser"
	password := "testpass"
	authData := []byte{0x01} // 认证版本
	authData = append(authData, byte(len(username)))
	authData = append(authData, []byte(username)...)
	authData = append(authData, byte(len(password)))
	authData = append(authData, []byte(password)...)

	_, err = conn.Write(authData)
	if err != nil {
		t.Fatalf("发送认证数据失败: %v", err)
	}

	// 读取认证响应
	authResponse := make([]byte, 2)
	_, err = conn.Read(authResponse)
	if err != nil {
		t.Fatalf("读取认证响应失败: %v", err)
	}

	// 验证认证成功
	if authResponse[0] != 0x01 || authResponse[1] != 0x00 {
		t.Fatalf("期望认证成功，实际: %v", authResponse)
	}
}
