package c2

import (
	"errors"
	"server/core/dbpool"
	"server/factory"
	"server/model/sys"

	"gorm.io/gorm"
)

type CommandService struct{}

// SendCommand 向客户端发送命令
func (c *CommandService) SendCommand(id uint, command string) (err error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err = dbpool.ExecuteDBOperationAsyncAndWait("command_client_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return errors.New("未找到客户端")
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return errors.New("客户端不在线")
	}

	// 根据监听器类型发送命令
	return factory.SendCommandFactory(client, command)
}

// SendResize 向客户端发出resize命令
func (c *CommandService) SendResize(id uint, cols, rows uint16) (err error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	if err = dbpool.ExecuteDBOperationAsyncAndWait("resize_client_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&client).Error
	}); err != nil {
		return errors.New("未找到客户端")
	}

	// 检查客户端是否在线
	if client.Status != 1 {
		return errors.New("客户端不在线")
	}

	return factory.SendResizeFactory(client, cols, rows)
}
