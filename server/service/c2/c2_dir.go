package c2

import (
	"errors"
	"server/core/dbpool"
	"server/factory"
	"server/global"
	"server/model/request/fs"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"server/utils"
	"time"

	"go.uber.org/zap"

	"gorm.io/gorm"
)

type DirService struct{}

// CreateDir 创建目录
func (s *DirService) CreateDir(clientID uint, req fs.DirCreateRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "create_dir",
		SourcePath:      "",
		DestinationPath: req.Path,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("dir_create_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行创建目录任务
	//go s.executeCreateDirTask(task, req)
	go s.executeDirTask(task, req)
	return task.ID, nil
}

// ListDir 列出目录内容
func (s *DirService) ListDir(clientID uint, req fs.DirListRequest) (uint64, error) {

	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "list_dir",
		SourcePath:      "",
		DestinationPath: req.Path,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("dir_list_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	go s.executeDirTask(task, req)
	return task.ID, nil
}

func (s *DirService) executeDirTask(task *task.FileTransferTask, req interface{}) {
	// 获取客户端
	client, err := s.getOnlineClient(task.ClientID)
	if err != nil {
		utils.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	// 🚀 设置TaskID到请求中
	req = setTaskIDToRequest(req, task.ID)
	global.LOG.Info("dir请求发送", zap.Any("req", req))
	// 序列化请求数据
	reqBytes, err := utils.SerializerManager.Serialize(req)
	if err != nil {
		utils.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.Dir,
		},
		PacketData: &tlv.PacketData{
			Data: reqBytes,
		},
	}
	switch task.TaskType {
	case "create_dir":
		packet.Header.Code = tlv.DirCreate
	case "list_dir":
		packet.Header.Code = tlv.DirList
	case "move_dir":
		packet.Header.Code = tlv.DirMove
	case "delete_dir":
		packet.Header.Code = tlv.DirDelete
	case "copy_dir":
		packet.Header.Code = tlv.DirCopy
	case "list_disk":
		packet.Header.Code = tlv.DiskList
	}

	err = s.sendPacket(client, packet)
	if err != nil {
		utils.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}
	switch task.TaskType {
	case "create_dir":
		utils.UpdateTaskStatus(task.ID, "running", "目录创建请求已发送")
	case "list_dir":
		utils.UpdateTaskStatus(task.ID, "running", "目录列出请求已发送")
	case "move_dir":
		utils.UpdateTaskStatus(task.ID, "running", "目录移动请求已发送")
	case "delete_dir":
		utils.UpdateTaskStatus(task.ID, "running", "目录删除请求已发送")
	case "copy_dir":
		utils.UpdateTaskStatus(task.ID, "running", "目录复制请求已发送")
	case "list_disk":
		utils.UpdateTaskStatus(task.ID, "running", "磁盘列表请求已发送")
	}

}

// MoveDir 移动目录
func (s *DirService) MoveDir(clientID uint, req fs.DirMoveRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "move_dir",
		SourcePath:      req.Source,
		DestinationPath: req.Destination,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("dir_move_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行移动目录任务
	go s.executeDirTask(task, req)

	return task.ID, nil
}

// DeleteDir 删除目录
func (s *DirService) DeleteDir(clientID uint, req fs.DirDeleteRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "delete_dir",
		SourcePath:      req.Path,
		DestinationPath: "",
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("dir_delete_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行删除目录任务
	go s.executeDirTask(task, req)

	return task.ID, nil
}

// CopyDir 复制目录
func (s *DirService) CopyDir(clientID uint, req fs.DirCopyRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "copy_dir",
		SourcePath:      req.SourcePath,
		DestinationPath: req.DestinationPath,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("dir_copy_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行复制目录任务
	go s.executeDirTask(task, req)

	return task.ID, nil
}

// 辅助方法
func (s *DirService) getOnlineClient(clientID uint) (*sys.Client, error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_online_check", func(db *gorm.DB) error {
		return db.Where("id = ? AND status = ?", clientID, 1).First(&client).Error
	})

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("客户端不存在或不在线")
		}
		return nil, err
	}
	return &client, nil
}

func (s *DirService) sendPacket(client *sys.Client, packet *tlv.Packet) error {
	// 使用factory发送目录数据包到客户端
	return factory.SendPacketFactory(*client, packet)
}

// ListDisks 获取磁盘列表
func (s *DirService) ListDisks(clientID uint, req fs.DiskListRequest) (uint64, error) {
	// 创建任务记录
	task := &task.FileTransferTask{
		ClientID:        clientID,
		TaskType:        "list_disk",
		SourcePath:      "",
		DestinationPath: "disks",
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("dir_list_disk_task_create", func(db *gorm.DB) error {
		return db.Create(task).Error
	}); err != nil {
		return 0, err
	}

	// 异步执行获取磁盘列表任务
	go s.executeDirTask(task, req)

	return task.ID, nil
}

// setTaskIDToRequest 设置TaskID到请求中（通用辅助函数）
func setTaskIDToRequest(req interface{}, taskID uint64) interface{} {
	switch r := req.(type) {
	case *fs.DirCreateRequest:
		r.TaskID = taskID
		return r
	case *fs.DirListRequest:
		r.TaskID = taskID
		return r
	case *fs.DirMoveRequest:
		r.TaskID = taskID
		return r
	case *fs.DirDeleteRequest:
		r.TaskID = taskID
		return r
	case *fs.DirCopyRequest:
		r.TaskID = taskID
		return r
	case *fs.DiskListRequest:
		r.TaskID = taskID
		return r
	case fs.DirCreateRequest:
		r.TaskID = taskID
		return r
	case fs.DirListRequest:
		r.TaskID = taskID
		return r
	case fs.DirMoveRequest:
		r.TaskID = taskID
		return r
	case fs.DirDeleteRequest:
		r.TaskID = taskID
		return r
	case fs.DirCopyRequest:
		r.TaskID = taskID
		return r
	case fs.DiskListRequest:
		r.TaskID = taskID
		return r
	default:
		global.LOG.Error("未知的请求类型", zap.Any("req", req))
		return req
	}
}
