package c2

import (
	"math/rand"
	"server/core/dbpool"
	"server/core/shutdown"
	"server/global"
	"server/model/basic"
	"server/model/request/heartbeat"
	heartbeat2 "server/model/response/heartbeat"
	"server/model/sys"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type HeartbeatService struct{}

// StartOfflineDetection 启动离线检测服务
func (s *HeartbeatService) StartOfflineDetection() {
	go s.offlineDetectionWorker()
	global.LOG.Info("离线检测服务已启动")
}

// offlineDetectionWorker 离线检测工作协程
func (s *HeartbeatService) offlineDetectionWorker() {
	// 每30秒检查一次客户端状态
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	// 🚀 注册goroutine到关闭管理器
	shutdown.RegisterGoroutine()
	defer shutdown.UnregisterGoroutine()

	for {
		select {
		case <-shutdown.GetShutdownChannel():
			global.LOG.Info("收到服务器关闭信号，停止离线检测服务")
			return
		case <-ticker.C:
			s.checkOfflineClients()
		}
	}
}

// checkOfflineClients 检查离线客户端
func (s *HeartbeatService) checkOfflineClients() {
	// 获取所有在线客户端
	var clients []sys.Client
	// 🚀 使用数据库连接池获取在线客户端列表
	if err := dbpool.ExecuteDBOperationAsyncAndWait("online_clients_heartbeat", func(db *gorm.DB) error {
		return db.Where("status = ?", 1).Find(&clients).Error
	}); err != nil {
		global.LOG.Error("获取在线客户端列表失败", zap.Error(err))
		return
	}

	now := time.Now()
	offlineCount := 0

	for _, client := range clients {
		// 计算客户端最后活动时间
		timeSinceLastActive := now.Sub(client.LastActiveAt)

		// 获取监听器配置
		var listener sys.Listener
		// 🚀 使用数据库连接池查询监听器
		if err := dbpool.ExecuteDBOperationAsyncAndWait("listener_heartbeat_query", func(db *gorm.DB) error {
			return db.Where("id = ?", client.ListenerID).First(&listener).Error
		}); err != nil {
			continue
		}

		// 计算超时阈值：心跳间隔 * 最大超时次数 * 2 (给一些缓冲时间)
		heartbeatInterval := time.Duration(listener.PingDuration) * time.Second
		if heartbeatInterval == 0 {
			heartbeatInterval = 30 * time.Second // 默认30秒
		}

		maxTimeoutCount := listener.MaxTimeoutCount
		if maxTimeoutCount == 0 {
			maxTimeoutCount = 5 // 默认5次
		}

		timeoutThreshold := heartbeatInterval * time.Duration(maxTimeoutCount) * 2

		// 如果超过阈值，标记为离线
		if timeSinceLastActive > timeoutThreshold {
			if err := s.markClientOffline(client.ID); err != nil {
				global.LOG.Error("标记客户端离线失败",
					zap.Uint("clientID", client.ID),
					zap.String("remoteAddr", client.RemoteAddr),
					zap.Error(err))
			} else {
				global.LOG.Info("客户端已标记为离线",
					zap.Uint("clientID", client.ID),
					zap.String("remoteAddr", client.RemoteAddr),
					zap.Duration("inactiveTime", timeSinceLastActive))
				offlineCount++
			}
		}
	}

	if offlineCount > 0 {
		global.LOG.Info("离线检测完成", zap.Int("offlineCount", offlineCount))
	}
}

// markClientOffline 标记客户端为离线
func (s *HeartbeatService) markClientOffline(clientID uint) error {
	// 🚀 使用数据库连接池标记客户端离线
	return dbpool.ExecuteDBOperationAsyncAndWait("client_mark_offline", func(db *gorm.DB) error {
		return db.Model(&sys.Client{}).Where("id = ?", clientID).Updates(map[string]interface{}{
			"status":         0,
			"last_active_at": time.Now(),
		}).Error
	})
}

// CreateHeartbeatRequest 创建心跳请求
func (s *HeartbeatService) CreateHeartbeatRequest(clientID string, heartbeatType uint8) *heartbeat.HeartbeatRequest {
	return &heartbeat.HeartbeatRequest{
		ClientID:    clientID,
		Timestamp:   time.Now(),
		SequenceNum: s.generateSequenceNumber(),
		SystemInfo:  s.getSystemStatus(),
		NetworkInfo: s.getNetworkStatus(),
		Type:        heartbeatType,
		Jitter:      s.generateJitter(),
	}
}

// CreateHeartbeatResponse 创建心跳响应
func (s *HeartbeatService) CreateHeartbeatResponse(sequenceNum uint64, responseType uint8) *heartbeat2.HeartbeatResponse {
	return &heartbeat2.HeartbeatResponse{
		ServerID:    global.CONFIG.Server.ID, // 可以从配置中获取
		Timestamp:   time.Now(),
		SequenceNum: sequenceNum,
		ServerInfo:  s.getServerStatus(),
		ClientInfo:  s.getClientManagement(),
		Config:      s.getHeartbeatConfig(),
		Type:        responseType,
		Jitter:      s.generateJitter(),
	}
}

// generateSequenceNumber 生成序列号
func (s *HeartbeatService) generateSequenceNumber() uint64 {
	return uint64(time.Now().UnixNano())
}

// generateJitter 生成随机抖动值（毫秒）
func (s *HeartbeatService) generateJitter() int {
	// 生成0-5000毫秒的随机抖动
	return rand.Intn(5000)
}

// getSystemStatus 获取系统状态（客户端填充）
func (s *HeartbeatService) getSystemStatus() basic.SystemStatus {
	return basic.SystemStatus{
		CPUUsage:    0.0, // 客户端填充
		MemoryUsage: 0.0, // 客户端填充
		DiskUsage:   0.0, // 客户端填充
		Uptime:      0,   // 客户端填充
		LoadAvg:     0.0, // 客户端填充
	}
}

// getNetworkStatus 获取网络状态（客户端填充）
func (s *HeartbeatService) getNetworkStatus() basic.NetworkStatus {
	return basic.NetworkStatus{
		LocalIP:    "",  // 客户端填充
		PublicIP:   "",  // 客户端填充
		Latency:    0,   // 客户端填充
		PacketLoss: 0.0, // 客户端填充
		Bandwidth:  0,   // 客户端填充
	}
}

// getServerStatus 获取服务器状态
func (s *HeartbeatService) getServerStatus() basic.ServerStatus {
	return basic.ServerStatus{
		Status:    1,                            // 1=正常
		Timestamp: time.Now().Unix(),            // 服务器时间戳
		Version:   global.CONFIG.Server.Version, // 从配置获取版本
	}
}

// getClientManagement 获取客户端管理信息
func (s *HeartbeatService) getClientManagement() basic.ClientManagement {
	return basic.ClientManagement{
		ShouldReconnect: false,
		NewServerAddr:   "",
		ConfigUpdate:    false,
		Commands:        []string{},
	}
}

// getHeartbeatConfig 获取心跳配置
func (s *HeartbeatService) getHeartbeatConfig() basic.HeartbeatConfig {
	// 🚀 从数据库获取全局心跳配置
	configService := HeartbeatConfigService{}
	config, err := configService.GetHeartbeatConfig(0) // 0表示全局配置
	if err != nil {
		// 如果获取失败，返回默认配置
		return basic.HeartbeatConfig{
			Interval:    30,   // 30秒间隔
			Timeout:     10,   // 10秒超时
			MaxRetries:  5,    // 最大5次重试
			JitterRange: 5000, // 5秒抖动范围
		}
	}

	return basic.HeartbeatConfig{
		Interval:    config.Interval,
		Timeout:     config.Timeout,
		MaxRetries:  config.MaxRetries,
		JitterRange: config.JitterRange,
	}
}

// UpdateClientLastActive 更新客户端最后活动时间
func (s *HeartbeatService) UpdateClientLastActive(clientID uint) error {
	// 🚀 使用数据库连接池更新客户端最后活动时间
	return dbpool.ExecuteDBOperationAsyncAndWait("client_last_active_heartbeat", func(db *gorm.DB) error {
		return db.Model(&sys.Client{}).Where("id = ?", clientID).Update("last_active_at", time.Now()).Error
	})
}

// GetHeartbeatInterval 获取心跳间隔（带抖动）
func (s *HeartbeatService) GetHeartbeatInterval(baseInterval time.Duration) time.Duration {
	// 添加±20%的随机抖动
	jitterPercent := 0.2
	jitter := time.Duration(float64(baseInterval) * jitterPercent * (rand.Float64()*2 - 1))
	return baseInterval + jitter
}
