package sys

import (
	"bytes"
	"server/core/dbpool"
	"server/core/manager"
	"server/core/workerpool"
	"server/global"
	"server/model/request"
	"server/model/response"
	"server/model/sys"
	"text/template"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type NotificationService struct{}

// CreateNotification 创建单个通知
func (s *NotificationService) CreateNotification(req request.NotificationCreateRequest) error {
	// 获取通知模板
	tmpl := sys.GetNotificationTemplate(req.Type)
	
	// 渲染标题和内容
	title, err := s.renderTemplate(tmpl.Title, req.Data)
	if err != nil {
		title = tmpl.Title // 使用默认标题
	}
	
	content, err := s.renderTemplate(tmpl.Content, req.Data)
	if err != nil {
		content = tmpl.Content // 使用默认内容
	}

	// 如果请求中没有指定级别，使用模板默认级别
	level := req.Level
	if level == "" {
		level = tmpl.Level
	}

	// 创建通知记录
	notification := &sys.SysNotification{
		UserID:  req.UserID,
		Type:    req.Type,
		Level:   level,
		Title:   title,
		Content: content,
	}

	// 设置数据
	if err := notification.SetData(req.Data); err != nil {
		global.LOG.Error("设置通知数据失败", zap.Error(err))
	}

	// 异步保存到数据库
	err = dbpool.ExecuteDBOperationAsyncAndWait("notification_create", func(db *gorm.DB) error {
		return db.Create(notification).Error
	})

	if err != nil {
		global.LOG.Error("创建通知失败",
			zap.Uint("userID", req.UserID),
			zap.String("type", string(req.Type)),
			zap.Error(err))
		return err
	}

	// 异步发送SSE通知
	task := workerpool.NewGeneralTask("sse_notification", func() error {
		s.sendSSENotification(notification, "create")
		return nil
	})
	workerpool.SubmitGeneralTask(task)

	global.LOG.Info("通知创建成功",
		zap.Uint("notificationID", notification.ID),
		zap.Uint("userID", req.UserID),
		zap.String("type", string(req.Type)))

	return nil
}

// CreateNotificationForOwner 为资源拥有者创建通知
func (s *NotificationService) CreateNotificationForOwner(ownerID uint, notificationType sys.NotificationType, data sys.NotificationData) error {
	// 检查通知设置是否启用
	if !s.isNotificationEnabled(notificationType) {
		global.LOG.Debug("通知类型已禁用，跳过发送",
			zap.String("type", string(notificationType)))
		return nil
	}

	req := request.NotificationCreateRequest{
		UserID: ownerID,
		Type:   notificationType,
		Data:   data,
	}

	return s.CreateNotification(req)
}

// BatchCreateNotifications 批量创建通知
func (s *NotificationService) BatchCreateNotifications(req request.NotificationBatchCreateRequest) error {
	// 检查通知设置是否启用
	if !s.isNotificationEnabled(req.Type) {
		global.LOG.Debug("通知类型已禁用，跳过批量发送",
			zap.String("type", string(req.Type)))
		return nil
	}

	// 获取通知模板
	tmpl := sys.GetNotificationTemplate(req.Type)
	
	// 渲染标题和内容
	title, err := s.renderTemplate(tmpl.Title, req.Data)
	if err != nil {
		title = tmpl.Title
	}
	
	content, err := s.renderTemplate(tmpl.Content, req.Data)
	if err != nil {
		content = tmpl.Content
	}

	// 如果请求中没有指定级别，使用模板默认级别
	level := req.Level
	if level == "" {
		level = tmpl.Level
	}

	// 批量创建通知记录
	notifications := make([]sys.SysNotification, len(req.UserIDs))
	for i, userID := range req.UserIDs {
		notification := &notifications[i]
		notification.UserID = userID
		notification.Type = req.Type
		notification.Level = level
		notification.Title = title
		notification.Content = content
		
		// 设置数据
		if err := notification.SetData(req.Data); err != nil {
			global.LOG.Error("设置通知数据失败", zap.Error(err))
		}
	}

	// 异步批量保存到数据库
	err = dbpool.ExecuteDBOperationAsyncAndWait("notification_batch_create", func(db *gorm.DB) error {
		return db.CreateInBatches(notifications, 100).Error
	})

	if err != nil {
		global.LOG.Error("批量创建通知失败",
			zap.Int("userCount", len(req.UserIDs)),
			zap.String("type", string(req.Type)),
			zap.Error(err))
		return err
	}

	// 异步发送SSE通知
	task := workerpool.NewGeneralTask("batch_sse_notification", func() error {
		for _, notification := range notifications {
			s.sendSSENotification(&notification, "create")
		}
		return nil
	})
	workerpool.SubmitGeneralTask(task)

	global.LOG.Info("批量通知创建成功",
		zap.Int("count", len(notifications)),
		zap.String("type", string(req.Type)))

	return nil
}

// GetNotificationList 获取通知列表
func (s *NotificationService) GetNotificationList(userID uint, req request.NotificationListRequest) (response.NotificationListResponse, error) {
	var notifications []sys.SysNotification
	var total int64

	err := dbpool.ExecuteDBOperationAsyncAndWait("notification_list", func(db *gorm.DB) error {
		query := db.Model(&sys.SysNotification{}).Where("user_id = ?", userID)

		// 类型过滤
		if req.Type != "" {
			query = query.Where("type = ?", req.Type)
		}

		// 级别过滤
		if req.Level != "" {
			query = query.Where("level = ?", req.Level)
		}

		// 已读状态过滤
		if req.ReadOnly != nil {
			if *req.ReadOnly {
				query = query.Where("read_at IS NOT NULL")
			} else {
				query = query.Where("read_at IS NULL")
			}
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 分页查询
		offset := (req.Page - 1) * req.PageSize
		return query.Order("created_at DESC").
			Offset(offset).
			Limit(req.PageSize).
			Find(&notifications).Error
	})

	if err != nil {
		return response.NotificationListResponse{}, err
	}

	// 获取统计信息
	stats, _ := s.GetNotificationStats(userID)

	return response.NotificationListResponse{
		List:     response.ConvertNotificationListToResponse(notifications),
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Stats:    stats,
	}, nil
}

// GetNotificationStats 获取通知统计信息
func (s *NotificationService) GetNotificationStats(userID uint) (sys.NotificationStats, error) {
	var stats sys.NotificationStats

	err := dbpool.ExecuteDBOperationAsyncAndWait("notification_stats", func(db *gorm.DB) error {
		// 总数
		if err := db.Model(&sys.SysNotification{}).
			Where("user_id = ?", userID).
			Count(&stats.Total).Error; err != nil {
			return err
		}

		// 未读数
		if err := db.Model(&sys.SysNotification{}).
			Where("user_id = ? AND read_at IS NULL", userID).
			Count(&stats.Unread).Error; err != nil {
			return err
		}

		// 已读数
		stats.Read = stats.Total - stats.Unread
		return nil
	})

	return stats, err
}

// MarkAsRead 标记通知为已读
func (s *NotificationService) MarkAsRead(userID uint, req request.NotificationMarkReadRequest) error {
	err := dbpool.ExecuteDBOperationAsyncAndWait("notification_mark_read", func(db *gorm.DB) error {
		return db.Model(&sys.SysNotification{}).
			Where("user_id = ? AND id IN ?", userID, req.IDs).
			Update("read_at", time.Now()).Error
	})

	if err != nil {
		return err
	}

	// 异步发送SSE更新通知
	task := workerpool.NewGeneralTask("mark_read_sse_notification", func() error {
		stats, _ := s.GetNotificationStats(userID)
		sseData := response.SSENotificationData{
			Type:      "notification",
			Action:    "stats_update",
			Stats:     stats,
			Timestamp: time.Now(),
		}

		if manager.GlobalSSEManager != nil {
			manager.GlobalSSEManager.SendToUser(userID, sseData)
		}
		return nil
	})
	workerpool.SubmitGeneralTask(task)

	return nil
}

// MarkAllAsRead 标记所有通知为已读
func (s *NotificationService) MarkAllAsRead(userID uint, req request.NotificationMarkAllReadRequest) error {
	err := dbpool.ExecuteDBOperationAsyncAndWait("notification_mark_all_read", func(db *gorm.DB) error {
		query := db.Model(&sys.SysNotification{}).
			Where("user_id = ? AND read_at IS NULL", userID)

		if req.Type != "" {
			query = query.Where("type = ?", req.Type)
		}

		if req.Level != "" {
			query = query.Where("level = ?", req.Level)
		}

		return query.Update("read_at", time.Now()).Error
	})

	if err != nil {
		return err
	}

	// 异步发送SSE更新通知
	task := workerpool.NewGeneralTask("mark_all_read_sse_notification", func() error {
		stats, _ := s.GetNotificationStats(userID)
		sseData := response.SSENotificationData{
			Type:      "notification",
			Action:    "stats_update",
			Stats:     stats,
			Timestamp: time.Now(),
		}

		if manager.GlobalSSEManager != nil {
			manager.GlobalSSEManager.SendToUser(userID, sseData)
		}
		return nil
	})
	workerpool.SubmitGeneralTask(task)

	return nil
}

// DeleteNotifications 删除通知
func (s *NotificationService) DeleteNotifications(userID uint, req request.NotificationDeleteRequest) error {
	err := dbpool.ExecuteDBOperationAsyncAndWait("notification_delete", func(db *gorm.DB) error {
		return db.Where("user_id = ? AND id IN ?", userID, req.IDs).
			Delete(&sys.SysNotification{}).Error
	})

	if err != nil {
		return err
	}

	// 异步发送SSE更新通知
	task := workerpool.NewGeneralTask("cleanup_sse_notification", func() error {
		stats, _ := s.GetNotificationStats(userID)
		sseData := response.SSENotificationData{
			Type:      "notification",
			Action:    "stats_update",
			Stats:     stats,
			Timestamp: time.Now(),
		}

		if manager.GlobalSSEManager != nil {
			manager.GlobalSSEManager.SendToUser(userID, sseData)
		}
		return nil
	})
	workerpool.SubmitGeneralTask(task)

	return nil
}

// renderTemplate 渲染模板
func (s *NotificationService) renderTemplate(tmplStr string, data sys.NotificationData) (string, error) {
	tmpl, err := template.New("notification").Parse(tmplStr)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// sendSSENotification 发送SSE通知
func (s *NotificationService) sendSSENotification(notification *sys.SysNotification, action string) {
	global.LOG.Info("📤 准备发送SSE通知",
		zap.Uint("notification_id", notification.ID),
		zap.Uint("user_id", notification.UserID),
		zap.String("action", action),
		zap.String("title", notification.Title))

	if manager.GlobalSSEManager == nil {
		global.LOG.Error("❌ GlobalSSEManager为空，无法发送SSE通知")
		return
	}

	// 获取统计信息
	stats, _ := s.GetNotificationStats(notification.UserID)
	global.LOG.Debug("📊 获取用户通知统计",
		zap.Uint("user_id", notification.UserID),
		zap.Int64("unread_count", stats.Unread))

	sseData := response.SSENotificationData{
		Type:         "notification",
		Action:       action,
		Notification: response.ConvertNotificationToResponse(notification),
		Stats:        stats,
		Timestamp:    time.Now(),
	}

	global.LOG.Info("🚀 发送SSE通知到用户",
		zap.Uint("user_id", notification.UserID),
		zap.String("type", sseData.Type),
		zap.String("action", sseData.Action))

	if err := manager.GlobalSSEManager.SendToUser(notification.UserID, sseData); err != nil {
		global.LOG.Error("❌ 发送SSE通知失败",
			zap.Uint("userID", notification.UserID),
			zap.Error(err))
	} else {
		global.LOG.Info("✅ SSE通知发送成功",
			zap.Uint("userID", notification.UserID),
			zap.String("title", notification.Title))
	}
}

// isNotificationEnabled 检查通知类型是否启用
func (s *NotificationService) isNotificationEnabled(notificationType sys.NotificationType) bool {
	// 从配置中检查通知是否启用
	switch notificationType {
	case sys.NotificationClientOnline, sys.NotificationClientOffline, sys.NotificationClientDeleted:
		return global.VP.GetBool("notification.clientStatus")
	default:
		return true // 默认启用其他类型的通知
	}
}

// CleanupOldNotifications 清理旧通知（保留最近的N条）
func (s *NotificationService) CleanupOldNotifications(userID uint, keepCount int) error {
	return dbpool.ExecuteDBOperationAsyncAndWait("notification_cleanup", func(db *gorm.DB) error {
		// 获取需要保留的通知ID
		var keepIDs []uint
		if err := db.Model(&sys.SysNotification{}).
			Where("user_id = ?", userID).
			Order("created_at DESC").
			Limit(keepCount).
			Pluck("id", &keepIDs).Error; err != nil {
			return err
		}

		// 删除不在保留列表中的通知
		if len(keepIDs) > 0 {
			return db.Where("user_id = ? AND id NOT IN ?", userID, keepIDs).
				Delete(&sys.SysNotification{}).Error
		}

		return nil
	})
}
