package c2

import (
	"github.com/gin-gonic/gin"
)

type ProcessRoute struct{}

// InitProcRoute 初始化进程管理路由
func (p *ProcessRoute) InitProcRoute(Router *gin.RouterGroup) {
	processRouter := Router.Group("process")
	{
		// 进程操作
		processRouter.POST("/list/:clientId", procApi.ListProc)
		processRouter.POST("/kill/:clientId", procApi.KillProcess)
		processRouter.POST("/start/:clientId", procApi.StartProcess)
		processRouter.POST("/details/:clientId", procApi.GetProcessDetails)
		processRouter.POST("/suspend/:clientId", procApi.SuspendProcess)
		processRouter.POST("/resume/:clientId", procApi.ResumeProcess)
	}
}
