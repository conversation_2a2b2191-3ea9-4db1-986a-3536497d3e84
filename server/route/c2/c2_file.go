package c2

import (
	"github.com/gin-gonic/gin"
)

type FileRoute struct{}

// InitFileRoute 初始化文件管理路由
func (f *FileRoute) InitFileRoute(Router *gin.RouterGroup) {
	fileRouter := Router.Group("file")
	{
		// 文件信息
		fileRouter.POST("/:clientId/info", fileApi.GetFileInfo)

		// 文件操作
		fileRouter.POST("/:clientId/copy", fileApi.CopyFile)
		fileRouter.POST("/:clientId/delete", fileApi.DeleteFile)
		fileRouter.POST("/:clientId/move", fileApi.MoveFile)

		// 文件内容操作
		fileRouter.POST("/:clientId/read", fileApi.ReadFileContent)
		fileRouter.POST("/:clientId/write", fileApi.WriteFileContent)
		fileRouter.POST("/:clientId/create", fileApi.CreateFile)

		// 文件传输
		fileRouter.POST("/:clientId/download", fileApi.DownloadFileToServer)
		// 服务器文件管理
		fileRouter.POST("/:clientId/upload", fileApi.UploadFileToServerAndTransferToClient)
		fileRouter.POST("/:clientId/transfer-server-file", fileApi.TransferServerFileToClient)
		fileRouter.GET("/uploaded-files", fileApi.GetUploadedFiles)
		fileRouter.GET("/download-files", fileApi.GetDownloadFiles)

		// 服务器本地文件删除
		fileRouter.DELETE("/server-file", fileApi.DeleteServerFile)
		fileRouter.DELETE("/server-files", fileApi.DeleteServerFiles)

		// 传输任务管理
		fileRouter.GET("/transfer-tasks", fileApi.GetFileTransferTasks)
		fileRouter.GET("/transfer-tasks/:taskId", fileApi.GetFileTransferTask)
		fileRouter.DELETE("/cancel-transfer-tasks/:taskId", fileApi.CancelFileTransferTask)
	}
}
