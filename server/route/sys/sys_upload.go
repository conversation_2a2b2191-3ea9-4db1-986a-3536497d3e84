package sys

import (
	"github.com/gin-gonic/gin"
)

type UploadRouter struct{}

// InitUploadRouter 初始化上传路由
func (s *UploadRouter) InitUploadRouter(Router *gin.RouterGroup) {
	uploadRouter := Router.Group("upload")
	{
		// 获取上传配置信息
		uploadRouter.GET("/config", uploadApi.GetUploadConfig)

		// 上传文件到上传目录
		uploadRouter.POST("/upload-dir", uploadApi.UploadFileToServerUploadDir)

		// 上传文件到下载目录
		uploadRouter.POST("/download-dir", uploadApi.UploadFileToServerDownloadDir)
	}
}
