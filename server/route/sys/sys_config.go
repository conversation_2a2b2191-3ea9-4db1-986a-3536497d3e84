package sys

import (
	"github.com/gin-gonic/gin"
	"server/middleware"
)

type ConfigRoute struct{}

// InitConfigRoute 初始化配置相关路由
func (r *ConfigRoute) InitConfigRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	configRouter := Router.Group("/config")
	{
		configRouter.GET("", configApi.GetServerConfig) // 获取服务器配置信息
		
		// 需要JWT认证的路由
		configAuthRouter := configRouter.Group("").Use(middleware.JWTAuth())
		{
			// 常规配置管理
			configAuthRouter.GET("/general", configApi.GetGeneralConfig)                                    // 获取常规配置
			configAuthRouter.PUT("/general", middleware.AdminAuth(), configApi.UpdateGeneralConfig)        // 更新常规配置
			
			// 通知配置管理
			configAuthRouter.GET("/notification", configApi.GetNotificationConfig)                         // 获取通知配置
			configAuthRouter.PUT("/notification", middleware.AdminAuth(), configApi.UpdateNotificationConfig) // 更新通知配置
			
			// 配置重置
			configAuthRouter.POST("/reset", middleware.AdminAuth(), configApi.ResetConfig)                 // 重置配置
		}
	}

	return configRouter
}
