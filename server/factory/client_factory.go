package factory

import (
	"errors"
	"fmt"
	"server/core/dbpool"
	"server/core/listener/pipe"
	"server/core/listener/tcp"
	"server/global"
	"server/model/sys"
	"server/model/tlv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func DeleteClientFactory(client sys.Client) (err error) {
	// 先尝试关闭连接，避免在事务中长时间持有锁
	switch client.ListenerType {
	// 如果是pipe类型的监听器，尝试关闭连接
	case "pipe":
		// 获取监听器
		var listener sys.Listener
		// 🚀 使用数据库连接池查询监听器
		if err = dbpool.ExecuteDBOperationAsyncAndWait("listener_query_pipe", func(db *gorm.DB) error {
			return db.Where("id = ?", client.ListenerID).First(&listener).Error
		}); err != nil {
			global.LOG.Error("获取监听器失败", zap.Error(err))
			// 即使获取监听器失败，也继续执行，不影响客户端删除
		} else {
			// 尝试关闭连接
			pipe.PipeManager.CloseConnection(client.ListenerID, client.RemoteAddr)
		}
	case "tcp":
		// 获取监听器
		var listener sys.Listener
		// 🚀 使用数据库连接池查询监听器
		if err = dbpool.ExecuteDBOperationAsyncAndWait("listener_query_tcp", func(db *gorm.DB) error {
			return db.Where("id = ?", client.ListenerID).First(&listener).Error
		}); err != nil {
			global.LOG.Error("获取监听器失败", zap.Error(err))
			// 即使获取监听器失败，也继续执行，不影响客户端删除
		} else {
			// 获取TCP监听器实例
			tcpListener, err := tcp.TCPManager.GetListener(listener.ID)
			if err != nil {
				global.LOG.Error("未找到TCP监听器实例")
				// 即使未找到监听器实例，也继续执行，不影响客户端删除
			} else {
				// 尝试关闭连接
				err = tcpListener.CloseConnection(client.RemoteAddr)
				if err != nil {
					global.LOG.Error("关闭TCP连接失败", zap.Error(err))
					// 即使关闭连接失败，也继续执行，不影响客户端删除
				}
			}
		}
	}

	// 等待一段时间，确保连接关闭操作完成
	time.Sleep(100 * time.Millisecond)

	// 🚀 使用重试机制删除客户端记录
	for retries := 0; retries < 5; retries++ {
		err = dbpool.ExecuteDBOperationAsyncAndWait("client_delete_retry", func(db *gorm.DB) error {
			return db.Delete(&client).Error
		})
		if err == nil {
			// 删除成功，异步发送通知
			go func() {
				// 通过反射或接口调用service层通知方法
				// 避免循环导入，这里暂时留空
				global.LOG.Debug("客户端删除成功",
					zap.Uint("client_id", client.ID),
					zap.String("hostname", client.Hostname))
			}()
			return nil
		}

		// 如果是数据库锁定错误，等待更长时间后重试
		if strings.Contains(err.Error(), "database is locked") {
			global.LOG.Warn("数据库锁定，等待后重试", zap.Int("retry", retries+1))
			time.Sleep(time.Duration(500*(retries+1)) * time.Millisecond)
		} else {
			// 其他错误，直接返回
			return err
		}
	}

	return err
}

func SendCommandFactory(client sys.Client, command string) (err error) {
	// 根据监听器类型发送命令
	switch client.ListenerType {
	case "pipe":
		// 获取监听器
		var listener sys.Listener
		// 🚀 使用数据库连接池查询监听器
		if err = dbpool.ExecuteDBOperationAsyncAndWait("listener_query_send_command", func(db *gorm.DB) error {
			return db.Where("id = ?", client.ListenerID).First(&listener).Error
		}); err != nil {
			return errors.New("未找到关联的监听器")
		}

		// 发送命令
		err = pipe.PipeManager.SendCommand(client.ListenerID, client.RemoteAddr, command)
		if err != nil {
			return fmt.Errorf("发送命令失败: %s", err.Error())
		}

		// 🚀 更新最后活动时间
		if err = dbpool.ExecuteDBOperationAsyncAndWait("client_last_active_update_1", func(db *gorm.DB) error {
			return db.Model(&client).Update("last_active_at", time.Now()).Error
		}); err != nil {
			global.LOG.Error("更新客户端最后活动时间失败", zap.Error(err))
			// 不返回错误，不影响命令发送
		}

		return nil
	case "tcp":
		// 获取监听器
		var listener sys.Listener
		// 🚀 使用数据库连接池查询监听器
		if err = dbpool.ExecuteDBOperationAsyncAndWait("listener_query_tcp_command", func(db *gorm.DB) error {
			return db.Where("id = ?", client.ListenerID).First(&listener).Error
		}); err != nil {
			return errors.New("未找到关联的监听器")
		}

		// 获取TCP监听器实例
		tcpListener, err := tcp.TCPManager.GetListener(listener.ID)
		if err != nil {
			return errors.New("未找到TCP监听器实例")
		}
		// 发送命令
		err = tcpListener.SendCommand(client.RemoteAddr, command)
		if err != nil {
			return fmt.Errorf("发送命令失败: %s", err.Error())
		}

		// 🚀 更新最后活动时间
		if err = dbpool.ExecuteDBOperationAsyncAndWait("client_last_active_update_2", func(db *gorm.DB) error {
			return db.Model(&client).Update("last_active_at", time.Now()).Error
		}); err != nil {
			global.LOG.Error("更新客户端最后活动时间失败", zap.Error(err))
			// 不返回错误，不影响命令发送
		}

		return nil
	default:
		return errors.New("不支持的监听器类型")
	}
}

// SendFilePacketFactory 发送文件数据包到客户端
func SendPacketFactory(client sys.Client, packet *tlv.Packet) error {
	switch client.ListenerType {
	case "tcp":
		// 获取监听器
		var listener sys.Listener
		// 🚀 使用数据库连接池查询监听器
		if err := dbpool.ExecuteDBOperationAsyncAndWait("listener_query_resize_pipe", func(db *gorm.DB) error {
			return db.Where("id = ?", client.ListenerID).First(&listener).Error
		}); err != nil {
			return errors.New("未找到关联的监听器")
		}

		// 获取TCP监听器实例
		tcpListener, err := tcp.TCPManager.GetListener(listener.ID)
		if err != nil {
			return errors.New("未找到TCP监听器实例")
		}

		// 发送数据包
		err = tcpListener.SendPacket(client.RemoteAddr, packet)
		if err != nil {
			return fmt.Errorf("发送文件数据包失败: %s", err.Error())
		}

		// 🚀 更新最后活动时间
		if err = dbpool.ExecuteDBOperationAsyncAndWait("client_last_active_update_3", func(db *gorm.DB) error {
			return db.Model(&client).Update("last_active_at", time.Now()).Error
		}); err != nil {
			global.LOG.Error("更新客户端最后活动时间失败", zap.Error(err))
			// 不返回错误，不影响数据包发送
		}

		return nil
	default:
		return errors.New("不支持的监听器类型")
	}
}

func SendResizeFactory(client sys.Client, cols, rows uint16) error {
	switch client.ListenerType {
	case "tcp":
		var listener sys.Listener
		// 🚀 使用数据库连接池查询监听器
		if err := dbpool.ExecuteDBOperationAsyncAndWait("listener_query_resize_tcp", func(db *gorm.DB) error {
			return db.Where("id = ?", client.ListenerID).First(&listener).Error
		}); err != nil {
			return errors.New("未找到关联的监听器")
		}

		// 获取TCP监听器实例
		tcpListener, err := tcp.TCPManager.GetListener(listener.ID)
		if err != nil {
			return errors.New("未找到TCP监听器实例")
		}
		os := "windows"
		if strings.ToLower(client.OS) != "windows" {
			os = "unix"
		}
		err = tcpListener.SendResize(client.RemoteAddr, cols, rows, os)
		if err != nil {
			return fmt.Errorf("发送Resize数据包失败: %s", err.Error())
		}
		// 🚀 更新最后活动时间
		if err = dbpool.ExecuteDBOperationAsyncAndWait("client_last_active_update_4", func(db *gorm.DB) error {
			return db.Model(&client).Update("last_active_at", time.Now()).Error
		}); err != nil {
			global.LOG.Error("更新客户端最后活动时间失败", zap.Error(err))
			// 不返回错误，不影响数据包发送
		}

		return nil
	case "pipe":
		return nil
	default:
		return errors.New("不支持的监听器类型")
	}
}

func DisconnectClientFactory(client sys.Client) (err error) {
	switch client.ListenerType {
	case "pipe":
		// 关闭连接
		pipe.PipeManager.CloseConnection(client.ListenerID, client.RemoteAddr)

		// 🚀 更新状态
		if err = dbpool.ExecuteDBOperationAsyncAndWait("client_status_offline_update_1", func(db *gorm.DB) error {
			return db.Model(&client).Updates(map[string]interface{}{
				"status":         0,
				"last_active_at": time.Now(),
			}).Error
		}); err != nil {
			return err
		}

		// 异步发送客户端离线通知
		go func() {
			global.LOG.Debug("客户端离线",
				zap.Uint("client_id", client.ID),
				zap.String("hostname", client.Hostname))
		}()

		return nil
	case "tcp":
		// 获取监听器
		var listener sys.Listener
		// 🚀 使用数据库连接池查询监听器
		if err = dbpool.ExecuteDBOperationAsyncAndWait("listener_query_disconnect_tcp", func(db *gorm.DB) error {
			return db.Where("id = ?", client.ListenerID).First(&listener).Error
		}); err != nil {
			return errors.New("未找到关联的监听器")
		}

		// 获取TCP监听器实例
		tcpListener, err := tcp.TCPManager.GetListener(listener.ID)
		if err != nil {
			return errors.New("未找到TCP监听器实例")
		}

		// 关闭连接
		err = tcpListener.CloseConnection(client.RemoteAddr)
		if err != nil {
			global.LOG.Error("关闭TCP连接失败", zap.Error(err))
			// 即使关闭连接失败，也继续更新状态
		}

		// 🚀 更新状态
		if err = dbpool.ExecuteDBOperationAsyncAndWait("client_status_offline_update_2", func(db *gorm.DB) error {
			return db.Model(&client).Updates(map[string]interface{}{
				"status":         0,
				"last_active_at": time.Now(),
			}).Error
		}); err != nil {
			return err
		}

		// 异步发送客户端离线通知
		go func() {
			global.LOG.Debug("客户端离线",
				zap.Uint("client_id", client.ID),
				zap.String("hostname", client.Hostname))
		}()

		return nil
	default:
		return errors.New("不支持的监听器类型")
	}
}
