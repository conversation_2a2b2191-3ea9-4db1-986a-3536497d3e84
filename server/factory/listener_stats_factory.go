package factory

import (
	"fmt"
	"server/core/stats"
	"server/core/listener/pipe"
	"server/core/listener/tcp"
)

// GetListenerStatsProvider 根据监听器类型获取统计提供者
func GetListenerStatsProvider(listenerType string) (stats.ListenerStatsProvider, error) {
	switch listenerType {
	case "tcp":
		return tcp.GetStatsProvider(), nil
	case "pipe":
		return pipe.GetStatsProvider(), nil
	default:
		return nil, fmt.Errorf("不支持的监听器类型: %s", listenerType)
	}
}

// GetAllListenerStats 获取所有类型监听器的统计信息
func GetAllListenerStats() []stats.ListenerStats {
	var allStats []stats.ListenerStats
	
	// 获取TCP监听器统计
	tcpProvider := tcp.GetStatsProvider()
	tcpStats := tcpProvider.GetAllListenerStats()
	allStats = append(allStats, tcpStats...)
	
	// 获取Pipe监听器统计
	pipeProvider := pipe.GetStatsProvider()
	pipeStats := pipeProvider.GetAllListenerStats()
	allStats = append(allStats, pipeStats...)
	
	return allStats
}

// GetConnectionStatsByType 按类型获取连接统计
func GetConnectionStatsByType() map[string]stats.ConnectionStats {
	stats := make(map[string]stats.ConnectionStats)
	
	// TCP连接统计
	tcpProvider := tcp.GetStatsProvider()
	stats["tcp"] = tcpProvider.GetConnectionStats()
	
	// Pipe连接统计
	pipeProvider := pipe.GetStatsProvider()
	stats["pipe"] = pipeProvider.GetConnectionStats()
	
	return stats
}

// GetTrafficStatsByType 按类型获取流量统计
func GetTrafficStatsByType() map[string]stats.TrafficStats {
	stats := make(map[string]stats.TrafficStats)
	
	// TCP流量统计
	tcpProvider := tcp.GetStatsProvider()
	stats["tcp"] = tcpProvider.GetTrafficStats()
	
	// Pipe流量统计
	pipeProvider := pipe.GetStatsProvider()
	stats["pipe"] = pipeProvider.GetTrafficStats()
	
	return stats
}

// GetActiveListenerCountByType 按类型获取活跃监听器数量
func GetActiveListenerCountByType() map[string]int {
	counts := make(map[string]int)
	
	// TCP活跃监听器数量
	tcpProvider := tcp.GetStatsProvider()
	counts["tcp"] = tcpProvider.GetActiveListenerCount()
	
	// Pipe活跃监听器数量
	pipeProvider := pipe.GetStatsProvider()
	counts["pipe"] = pipeProvider.GetActiveListenerCount()
	
	return counts
}

// ResetAllListenerStats 重置所有监听器统计
func ResetAllListenerStats() {
	// 重置TCP统计
	tcpProvider := tcp.GetStatsProvider()
	tcpProvider.ResetStats()
	
	// 重置Pipe统计
	pipeProvider := pipe.GetStatsProvider()
	pipeProvider.ResetStats()
}

// GetListenerStatsByID 根据ID和类型获取特定监听器统计
func GetListenerStatsByID(listenerType string, listenerID uint) (stats.ListenerStats, error) {
	provider, err := GetListenerStatsProvider(listenerType)
	if err != nil {
		return nil, err
	}
	
	stats := provider.GetListenerByID(listenerID)
	if stats == nil {
		return nil, fmt.Errorf("监听器 %d 不存在", listenerID)
	}
	
	return stats, nil
}
