package basic

import (
	"server/global"
	"time"
)

type Proxy struct {
	global.DBModel
	//基本信息
	ProxyID     string `json:"proxy_id" gorm:"column:proxy_id;uniqueIndex;not null;comment:代理唯一标识"`
	ClientID    uint   `json:"client_id" gorm:"column:client_id;not null;comment:关联的客户端ID"`
	ListenerID  uint   `json:"listener_id" gorm:"column:listener_id;comment:关联的监听器ID"`
	Name        string `json:"name" gorm:"column:name;not null;comment:代理名称"`
	Description string `json:"description" gorm:"column:description;comment:代理描述"`

	//配置
	Type         string `json:"type" binding:"required" validate:"oneof=forward reverse"`
	AllocMode    string `json:"alloc_mode"`
	UserPort     uint16 `json:"user_port"`   // 开放给用户的端口
	ClientPort   uint16 `json:"client_port"` // 开放给客户端的端口
	AuthRequired bool   `json:"auth_required" gorm:"column:auth_required;default:false;comment:是否需要认证"`
	Username     string `json:"username"`
	Password     string `json:"password"`
	AllowedIPs   string `json:"allowed_ips" gorm:"column:allowed_ips;type:text;comment:允许的IP列表(JSON)"`
	BlockedIPs   string `json:"blocked_ips" gorm:"column:blocked_ips;type:text;comment:阻止的IP列表(JSON)"`

	// 状态信息
	Status     int       `json:"status" gorm:"column:status;default:0;comment:状态(0:停止 1:运行中 2:错误 3:启动中)"`
	StartedAt  time.Time `json:"started_at" gorm:"column:started_at;comment:启动时间"`
	StoppedAt  time.Time `json:"stopped_at" gorm:"column:stopped_at;comment:停止时间"`
	LastError  string    `json:"last_error" gorm:"column:last_error;comment:最后错误信息"`
	ErrorCount int       `json:"error_count" gorm:"column:error_count;default:0;comment:错误次数"`

	// 统计信息
	TotalConnections  int64     `json:"total_connections" gorm:"column:total_connections;default:0;comment:总连接数"`
	ActiveConnections int       `json:"active_connections" gorm:"column:active_connections;default:0;comment:活跃连接数"`
	BytesTransferred  int64     `json:"bytes_transferred" gorm:"column:bytes_transferred;default:0;comment:传输字节数"`
	BytesReceived     int64     `json:"bytes_received" gorm:"column:bytes_received;default:0;comment:接收字节数"`
	LastActivity      time.Time `json:"last_activity" gorm:"column:last_activity;comment:最后活动时间"`

	// 版本控制和同步
	Version      int64     `json:"version" gorm:"column:version;default:1;comment:版本号"`
	LastSyncTime time.Time `json:"last_sync_time" gorm:"column:last_sync_time;comment:最后同步时间"`
	MemoryDirty  bool      `json:"-" gorm:"-;comment:内存数据是否有变更"`
}
