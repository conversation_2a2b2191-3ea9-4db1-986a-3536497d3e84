package utils

import (
	"encoding/json"
	"fmt"
)

// Serializer 通用序列化器
type Serializer struct{}

var SerializerManager = Serializer{}

func (s *Serializer) Serialize(data interface{}) ([]byte, error) {
	result, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("json序列化失败: %w", err)
	}
	return result, nil
}

func (s *Serializer) Deserialize(data []byte, target interface{}) error {
	if err := json.Unmarshal(data, target); err != nil {
		return fmt.Errorf("json反序列化失败: %w", err)
	}
	return nil
}

// Serialize 序列化任何结构体
// func (us *Serializer) Serialize(v interface{}) ([]byte, error) {
// 	val := reflect.ValueOf(v)
// 	if val.Kind() == reflect.Ptr {
// 		val = val.Elem()
// 	}

// 	if val.Kind() != reflect.Struct {
// 		return nil, errors.New("only structs can be serialized")
// 	}

// 	var buf bytes.Buffer
// 	encoder := gob.NewEncoder(&buf)

// 	// 注册已知特殊类型
// 	gob.Register(time.Time{})
// 	gob.Register(map[string]string{})
// 	gob.Register(map[string]interface{}{})

// 	// 使用反射遍历结构体字段
// 	for i := 0; i < val.NumField(); i++ {
// 		field := val.Field(i)
// 		fieldType := val.Type().Field(i)

// 		// 跳过非导出字段
// 		if fieldType.PkgPath != "" && !fieldType.Anonymous {
// 			continue
// 		}

// 		// 处理不同类型的字段
// 		switch field.Kind() {
// 		case reflect.Ptr:
// 			if field.IsNil() {
// 				// 写入nil标记
// 				if err := encoder.Encode(false); err != nil {
// 					return nil, err
// 				}
// 			} else {
// 				// 写入非nil标记
// 				if err := encoder.Encode(true); err != nil {
// 					return nil, err
// 				}
// 				// 递归序列化指针指向的值
// 				fieldData, err := us.Serialize(field.Interface())
// 				if err != nil {
// 					return nil, err
// 				}
// 				// 写入长度和数据
// 				if err := binary.Write(&buf, binary.BigEndian, uint32(len(fieldData))); err != nil {
// 					return nil, err
// 				}
// 				buf.Write(fieldData)
// 			}
// 		case reflect.Struct:
// 			// 检查是否为time.Time类型
// 			if field.Type() == reflect.TypeOf(time.Time{}) {
// 				// 直接编码time.Time
// 				if err := encoder.Encode(field.Interface()); err != nil {
// 					return nil, err
// 				}
// 			} else {
// 				// 递归序列化嵌套结构体
// 				fieldData, err := us.Serialize(field.Interface())
// 				if err != nil {
// 					return nil, err
// 				}
// 				// 写入长度和数据
// 				if err := binary.Write(&buf, binary.BigEndian, uint32(len(fieldData))); err != nil {
// 					return nil, err
// 				}
// 				buf.Write(fieldData)
// 			}
// 		case reflect.Slice, reflect.Array, reflect.Map:
// 			// 直接编码这些复杂类型
// 			if err := encoder.Encode(field.Interface()); err != nil {
// 				return nil, err
// 			}
// 		default:
// 			// 使用gob编码基本类型
// 			if err := encoder.Encode(field.Interface()); err != nil {
// 				return nil, err
// 			}
// 		}
// 	}

// 	return buf.Bytes(), nil
// }

// // Deserialize 反序列化到结构体
// func (us *Serializer) Deserialize(data []byte, v interface{}) error {
// 	ptrVal := reflect.ValueOf(v)
// 	if ptrVal.Kind() != reflect.Ptr || ptrVal.IsNil() {
// 		return errors.New("must pass a non-nil pointer to Deserialize")
// 	}

// 	val := ptrVal.Elem()
// 	if val.Kind() != reflect.Struct {
// 		return errors.New("can only deserialize into a struct pointer")
// 	}

// 	buf := bytes.NewBuffer(data)
// 	decoder := gob.NewDecoder(buf)

// 	// 注册已知特殊类型
// 	gob.Register(time.Time{})
// 	gob.Register(map[string]string{})
// 	gob.Register(map[string]interface{}{})

// 	// 使用反射遍历结构体字段
// 	for i := 0; i < val.NumField(); i++ {
// 		field := val.Field(i)
// 		fieldType := val.Type().Field(i)

// 		// 跳过非导出字段
// 		if fieldType.PkgPath != "" && !fieldType.Anonymous {
// 			continue
// 		}

// 		// 处理不同类型的字段
// 		switch field.Kind() {
// 		case reflect.Ptr:
// 			// 检查是否有值
// 			var hasValue bool
// 			if err := decoder.Decode(&hasValue); err != nil {
// 				return err
// 			}

// 			if hasValue {
// 				// 读取长度
// 				var length uint32
// 				if err := binary.Read(buf, binary.BigEndian, &length); err != nil {
// 					return err
// 				}

// 				// 读取数据
// 				fieldData := make([]byte, length)
// 				if _, err := buf.Read(fieldData); err != nil {
// 					return err
// 				}

// 				// 创建指针指向的新值
// 				ptrType := field.Type().Elem()
// 				newValue := reflect.New(ptrType).Interface()

// 				// 递归反序列化
// 				if err := us.Deserialize(fieldData, newValue); err != nil {
// 					return err
// 				}

// 				// 设置指针值
// 				field.Set(reflect.ValueOf(newValue))
// 			} else {
// 				// 设置为nil
// 				field.Set(reflect.Zero(field.Type()))
// 			}
// 		case reflect.Struct:
// 			// 检查是否为time.Time类型
// 			if field.Type() == reflect.TypeOf(time.Time{}) {
// 				// 直接解码time.Time
// 				var t time.Time
// 				if err := decoder.Decode(&t); err != nil {
// 					return err
// 				}
// 				field.Set(reflect.ValueOf(t))
// 			} else {
// 				// 读取长度
// 				var length uint32
// 				if err := binary.Read(buf, binary.BigEndian, &length); err != nil {
// 					return err
// 				}

// 				// 读取数据
// 				fieldData := make([]byte, length)
// 				if _, err := buf.Read(fieldData); err != nil {
// 					return err
// 				}

// 				// 递归反序列化
// 				if err := us.Deserialize(fieldData, field.Addr().Interface()); err != nil {
// 					return err
// 				}
// 			}
// 		case reflect.Slice, reflect.Array, reflect.Map:
// 			// 直接解码这些复杂类型
// 			fieldPtr := field.Addr().Interface()
// 			if err := decoder.Decode(fieldPtr); err != nil {
// 				return err
// 			}
// 		default:
// 			// 使用gob解码基本类型
// 			fieldPtr := field.Addr().Interface()
// 			if err := decoder.Decode(fieldPtr); err != nil {
// 				return err
// 			}
// 		}
// 	}

// 	return nil
// }
